using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Godot;
using MegaCrit.Sts2.Test.Helpers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.CodeGotchas;

[TestFixture]
public partial class ResourcePathTest
{
    private static readonly HashSet<string> _filesToCheck = [".cs", ".godot", ".import", ".tres", ".tscn"];
    private static readonly HashSet<string> _filesToIgnore = ["MultiAssetCache.cs", "NGameInfoUploader.cs", "ModManager.cs"];

    [Test]
    public void TestResourcePathsExist()
    {
        IEnumerable<string> allFiles = Directory.EnumerateFiles(PathHelper.Sts2ProjectDir, "*", SearchOption.AllDirectories)
            .Where(file => !file.Contains("addons")) // ignore gdextension files
            .Where(file => !file.Contains(".godot")) // ignore anything in the .godot directory
            .Where(file => _filesToCheck.Contains(Path.GetExtension(file).ToLowerInvariant()))
            .Where(file => _filesToIgnore.All(ignore => !file.Contains(ignore)));
        Regex resourcePathRegex = ResourceUrlRegex();
        List<string> missingFiles = [];

        foreach (string file in allFiles)
        {
            using StreamReader reader = new(file);
            while (reader.ReadLine() is { } line)
            {
                MatchCollection matches = resourcePathRegex.Matches(line);
                foreach (Match match in matches)
                {
                    if (match.Groups.Count <= 1) continue;
                    string resourcePath = match.Groups[1].Value;
                    if (file.EndsWith(".cs") && resourcePath.Contains('{'))
                    {
                        continue; // skip if the res url is string interpolated in C#
                    }

                    if (resourcePath.Contains(".godot/imported"))
                    {
                        continue; // skip if a godot imported path
                    }

                    string fullPath = ProjectSettings.GlobalizePath($"res://{resourcePath}");

                    if (!Path.Exists(fullPath))
                    {
                        missingFiles.Add($"File: '{file}' contains resource path='res://{resourcePath}' but it does not exist.");
                    }
                }
            }
        }

        if (missingFiles.Count != 0)
        {
            Assert.Fail("The following resource paths are missing:\n" + string.Join("\n", missingFiles));
        }
    }

    [GeneratedRegex("res://([^\\s\"]+)", RegexOptions.Compiled)]
    private static partial Regex ResourceUrlRegex();
}
