{"AMETHYST_BANGLE.description": "Card rewards can now contain Colorless cards.", "AMETHYST_BANGLE.flavor": "PLACEHOLDER: Tastes like blueberry.", "AMETHYST_BANGLE.title": "<PERSON><PERSON><PERSON><PERSON>", "ANCHOR.description": "Start each combat with [blue]{Block}[/blue] [gold]Block[/gold].", "ANCHOR.flavor": "Holding this miniature trinket, you feel heavier and more stable.", "ANCHOR.title": "<PERSON><PERSON>", "ANCIENT_BOOSTER_PACK.description": "Upon pickup, choose [blue]1[/blue] of [blue]2[/blue] packs of cards to add to your [gold]Deck[/gold].", "ANCIENT_BOOSTER_PACK.eventDescription": "[red]Lose ALL Gold.[/red] Choose [blue]1[/blue] of [blue]2[/blue] packs of cards to add to your [gold]Deck[/gold].", "ANCIENT_BOOSTER_PACK.flavor": "PLACEHOLDER", "ANCIENT_BOOSTER_PACK.title": "Ancient Booster Pack", "ANCIENT_TEA_SET.description": "Whenever you enter a Rest Site, start the next combat with {Energy:energyIcons()}.", "ANCIENT_TEA_SET.flavor": "The key to a refreshing night's rest.", "ANCIENT_TEA_SET.title": "Ancient Tea Set", "APEX_INSTINCT.description": "Choose [blue]{Cards}[/blue] [gold]{Cards:plural:Attack|Attacks}[/gold] in your [gold]Deck[/gold]. Enchant them with [purple]Instinct[/purple].", "APEX_INSTINCT.flavor": "PLACEHOLDER", "APEX_INSTINCT.title": "Apex Instinct", "ART_OF_WAR.description": "If you do not play any [gold]Attacks[/gold] during your turn, gain an additional {Energy:energyIcons()} next turn.", "ART_OF_WAR.flavor": "This ancient manuscript contains wisdom from a past age.", "ART_OF_WAR.title": "Art of War", "ASTROLABE.description": "Upon pickup, choose and [gold]Transform[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}, then [gold]Upgrade[/gold] them.", "ASTROLABE.eventDescription": "Choose and [gold]Transform[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}, then [gold]Upgrade[/gold] them.", "ASTROLABE.flavor": "A tool to glean invaluable knowledge from the stars.", "ASTROLABE.title": "Astrolabe", "BAG_OF_MARBLES.description": "At the start of each combat, apply [blue]{Vulnerable}[/blue] [gold]Vulnerable[/gold] to all enemies.", "BAG_OF_MARBLES.flavor": "A once popular toy in the City. Useful for throwing enemies off balance.", "BAG_OF_MARBLES.title": "Bag of Marbles", "BAG_OF_PREPARATION.description": "At the start of each combat, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "BAG_OF_PREPARATION.flavor": "Oversized adventurer's pack. Has many pockets and straps.", "BAG_OF_PREPARATION.title": "Bag of Preparation", "BARNACLED_PIPE.description": "Whenever you gain [gold]Block[/gold], gain [blue]{Block}[/blue] additional [gold]Block[/gold].", "BARNACLED_PIPE.flavor": "PLACEHOLDER", "BARNACLED_PIPE.title": "Barnacled Pipe", "BEDLAM_BEACON.description": "At the start of your turn, deal [blue]{Damage}[/blue] damage to all enemies.", "BEDLAM_BEACON.flavor": "PLACEHOLDER: A mysterious object that beckons you. You are left pondering.", "BEDLAM_BEACON.title": "Bedlam Beacon", "BELL_OF_TWILIGHT.description": "Upon pickup, gain [blue]{Gold}[/blue] Gold. Add [blue]{Elite}[/blue] extra [red]Elites[/red] to each map.", "BELL_OF_TWILIGHT.eventDescription": "Gain [blue]{Gold}[/blue] Gold. Add [blue]{Elite}[/blue] extra [red]Elites[/red] to each map.", "BELL_OF_TWILIGHT.flavor": "PLACEHOLDER: You hear a ringing in the distance, as the path before you morphs.", "BELL_OF_TWILIGHT.title": "Bell of Twilight", "BELT_BUCKLE.description": "While you have no Potions, you have [blue]{Dexterity}[/blue] additional [gold]Dexterity[/gold].", "BELT_BUCKLE.flavor": "PLACEHOLDER: Potions block its menacing aura.", "BELT_BUCKLE.title": "<PERSON>", "BENT_NAIL.description": "Whenever you receive unblocked attack damage, all enemies lose [blue]{Damage}[/blue] HP.", "BENT_NAIL.flavor": "PLACEHOLDER: When you have a bent nail, nothing looks like a hammer.", "BENT_NAIL.title": "<PERSON><PERSON>", "BIG_MUSHROOM.description": "At the start of each combat, draw [blue]{Cards}[/blue] less {Cards:plural:card|cards}.", "BIG_MUSHROOM.flavor": "PLACEHOLDER: There's no way you can finish the whole thing in one sitting.", "BIG_MUSHROOM.title": "Big Mushroom", "BLACK_BLOOD.description": "At the end of combat, heal [green]{Heal}[/green] HP.", "BLACK_BLOOD.flavor": "The rage grows darker.", "BLACK_BLOOD.title": "Black Blood", "BLACK_STAR.description": "[gold]Elites[/gold] drop an additional Relic when defeated.", "BLACK_STAR.flavor": "Originally discovered in the town of the serpent, beside a solitary candle.", "BLACK_STAR.title": "Black Star", "BLOOD_SOAKED_ROSE.description": "Gain {Energy:energyIcons()} at the start of each turn.", "BLOOD_SOAKED_ROSE.flavor": "PLACEHOLDER: The flower is a crimson red. Has a fragrance to die for.", "BLOOD_SOAKED_ROSE.title": "Blood-soaked Rose", "BLOOD_VIAL.description": "At the start of each combat, heal [green]{Heal}[/green] HP.", "BLOOD_VIAL.flavor": "A vial containing the blood of a pure and elder vampire.", "BLOOD_VIAL.title": "Blood Vial", "BONE_TEA.description": "At the start of the next {Combats:plural:combat|[blue]{}[/blue] combats}, [gold]Upgrade[/gold] your starting hand.", "BONE_TEA.flavor": "PLACEHOLDER: A tea steeped in hot water. High in calcium.", "BONE_TEA.title": "Bone Tea", "BOOK_OF_FIVE_RINGS.description": "Every [blue]{Cards}[/blue] {Cards:plural:card|cards} you add to your [gold]Deck[/gold], heal [green]{Heal}[/green] HP.", "BOOK_OF_FIVE_RINGS.flavor": "PLACEHOLDER: Not a book about a sporting event.", "BOOK_OF_FIVE_RINGS.title": "Book of Five Rings", "BOUND_PHYLACTERY.description": "At the start of combat, [gold]Summon[/gold] [blue]{<PERSON>mm<PERSON>}[/blue].", "BOUND_PHYLACTERY.flavor": "PLACEHOLDER", "BOUND_PHYLACTERY.title": "Bound Phylactery", "BOUNTY_BOX.description": "At the start of [gold]Elite[/gold] combats, gain [blue]{Strength}[/blue] [gold]Strength[/gold] and [blue]{Dexter<PERSON>}[/blue] [gold]Dexterity[/gold].", "BOUNTY_BOX.flavor": "PLACEHOLDER: Contains the names of the Spire's most wanted criminals.", "BOUNTY_BOX.title": "Bounty Box", "BRIMSTONE.description": "At the start of your turn, gain [blue]{SelfStrength}[/blue] [gold]Strength[/gold] and all enemies gain [blue]{EnemyStrength}[/blue] [gold]Strength[/gold].", "BRIMSTONE.flavor": "Emanates an infernal heat.", "BRIMSTONE.title": "Brimstone", "BROKEN_MIRROR.description": "Whenever a card is [gold]Exhausted[/gold] due to [gold]Ethereal[/gold], shuffle a [gold]Soul[/gold] into your [gold]Draw Pile[/gold].", "BROKEN_MIRROR.flavor": "PLACEHOLDER: The Necrobinder makes her own luck.", "BROKEN_MIRROR.title": "Broken Mirror", "BROKEN_ORBS.description": "Start the next {Uses:plural:combat|[blue]{}[/blue] combats} with an additional {Energy:energyIcons()}.", "BROKEN_ORBS.flavor": "PLACEHOLDER: Sorry defect", "BROKEN_ORBS.title": "Broken Orbs", "BUCKET_OF_COALS.description": "Enchant each [gold]Strike[/gold] in your [gold]Deck[/gold] with [purple]Tezcatara's Ember[/purple].", "BUCKET_OF_COALS.flavor": "PLACEHOLDER: It will burn forever.", "BUCKET_OF_COALS.title": "Bucket of Coals", "BURIED_SEED.description": "Gain {Energy:energyIcons()} at the start of the next [blue]{Combats}[/blue] combats.", "BURIED_SEED.flavor": "Hope you have a green thumb.", "BURIED_SEED.title": "<PERSON><PERSON><PERSON> Seed", "BURNING_BLOOD.description": "At the end of combat, heal [green]{Heal}[/green] HP.", "BURNING_BLOOD.flavor": "Your body's own blood burns with an undying rage.", "BURNING_BLOOD.title": "Burning Blood", "BYRDPIP.description": "Upon pickup, gain the card <PERSON> Swoop. A Byrdpi<PERSON> will accompany you in battles.", "BYRDPIP.flavor": "PLACEHOLDER: Shaped like a fren.", "BYRDPIP.title": "Byrdpip", "CALLING_BELL.description": "Upon pickup, obtain a unique [gold]Curse[/gold] and [blue]{Relics}[/blue] [gold]Relics[/gold].", "CALLING_BELL.eventDescription": "Obtain a unique [gold]Curse[/gold] and [blue]{Relics}[/blue] [gold]Relics[/gold].", "CALLING_BELL.flavor": "This dark iron bell rang 3 times when you found it, but now stays silent.", "CALLING_BELL.title": "Calling Bell", "CAPTAINS_WHEEL.description": "At the start of your 3rd turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "CAPTAINS_WHEEL.flavor": "Wood trinket carved with delicate precision. A name is carved into it but the language is foreign.", "CAPTAINS_WHEEL.title": "Captain's Wheel", "CAULDRON.description": "Upon pickup, brews [blue]{Potions}[/blue] random {Potions:plural:potion|potions}.", "CAULDRON.flavor": "The Merchant is actually a rather skilled potion brewer. Buy 4 get 1 free.", "CAULDRON.title": "<PERSON><PERSON><PERSON>", "CENTENNIAL_PUZZLE.description": "The first time you lose HP each combat, draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "CENTENNIAL_PUZZLE.flavor": "Upon solving the puzzle, you feel a powerful warmth in your chest.", "CENTENNIAL_PUZZLE.title": "Centennial Puzzle", "CHAIN_MAIL_PANTS.description": "At the start of combat, gain [blue]{Plating}[/blue] [gold]Plating[/gold].", "CHAIN_MAIL_PANTS.flavor": "PLACEHOLDER: The epitome of fashion.", "CHAIN_MAIL_PANTS.title": "Chain Mail Pants", "CHAOTIC_PLASMA.description": "Upon pickup, [gold]Transform[/gold] [blue]1[/blue] of your [gold]Strikes[/gold] and [blue]1[/blue] of your [gold]Defends[/gold].", "CHAOTIC_PLASMA.eventDescription": "[gold]Transform[/gold] [blue]1[/blue] of your [gold]Strikes[/gold] and [blue]1[/blue] of your [gold]Defends[/gold].", "CHAOTIC_PLASMA.flavor": "PLACEHOLDER", "CHAOTIC_PLASMA.title": "Chaotic Plasma", "CHARONS_ASHES.description": "Whenever you [gold]Exhaust[/gold] a card, deal [blue]{Damage}[/blue] damage to all enemies.", "CHARONS_ASHES.flavor": "<PERSON><PERSON> was said to be the god of rebirth, eternally dying and reviving in a burst of flame.", "CHARONS_ASHES.title": "<PERSON><PERSON>'s Ashes", "CHOICES_PARADOX.description": "At the start of combat, add [blue]1[/blue] of [blue]{Cards}[/blue] random [gold]Upgraded[/gold] cards to your [gold]Hand[/gold]. It costs [blue]0[/blue] this turn.", "CHOICES_PARADOX.flavor": "Don't think about it too hard.", "CHOICES_PARADOX.selectionScreenPrompt": "Select a Card to Add to Your [gold]Hand[/gold].", "CHOICES_PARADOX.title": "Choices Paradox", "CHOSEN_CHEESE.description": "At the end of combat, gain [blue]{MaxHp}[/blue] Max HP.", "CHOSEN_CHEESE.flavor": "Mmmm... Cheese.", "CHOSEN_CHEESE.title": "The Chosen Cheese", "CIRCLET.description": "It's a circlet", "CIRCLET.flavor": "You ran out of relics to find. Impressive!", "CIRCLET.title": "Circlet", "CLERICS_HEADPIECE.description": "Upon pickup, choose [blue]1[/blue] of [blue]3[/blue] [gold]Multiplayer Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "CLERICS_HEADPIECE.eventDescription": "Choose [blue]1[/blue] of [blue]3[/blue] [gold]Multiplayer Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "CLERICS_HEADPIECE.flavor": "PLACEHOLDER", "CLERICS_HEADPIECE.title": "<PERSON><PERSON><PERSON>'s Headpiece", "CLOCKWORK_SOUVENIR.description": "Start each combat with [blue]{Artifact}[/blue] [gold]Artifact[/gold].", "CLOCKWORK_SOUVENIR.flavor": "\"So many intricate gears.\"", "CLOCKWORK_SOUVENIR.title": "Clockwork Souvenir", "CLOUDY_CLAW.description": "[gold]Enchant[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards} with [purple]{EnchantmentName}[/purple].", "CLOUDY_CLAW.flavor": "PLACEHOLDER", "CLOUDY_CLAW.title": "<PERSON>y Claw", "COZY_CHAIR.description": "Upon pickup, add [blue]2[/blue] [gold]Relax[/gold] to your [gold]Deck[/gold].", "COZY_CHAIR.eventDescription": "Add [blue]2[/blue] [gold]Relax[/gold] to your [gold]Deck[/gold].", "COZY_CHAIR.flavor": "PLACEHOLDER", "COZY_CHAIR.title": "Cozy Chair", "CRACKED_CORE.description": "At the start of each combat, [gold]Channel[/gold] [blue]{Lightning}[/blue] [gold]Lightning[/gold].", "CRACKED_CORE.flavor": "The mysterious life force which powers the Automatons within the Spire. It appears to be cracked.", "CRACKED_CORE.title": "Cracked Core", "CRIMSON_PENDANT.description": "Start combat with [blue]{Intangible}[/blue] [gold]Intangible[/gold].", "CRIMSON_PENDANT.flavor": "PLACEHOLDER: You feel uncomfortable as you wear it around your neck.", "CRIMSON_PENDANT.title": "Crimson Pendant", "CURSED_KETTLE.description": "Whenever you defeat an [gold]Elite[/gold], the other [gold]Elites[/gold] in the act will gain [blue]{Strength}[/blue] [gold]Strength[/gold] at the start of combat.", "CURSED_KETTLE.flavor": "Placeholder Seems to get hotter and louder the further you go.", "CURSED_KETTLE.title": "Cursed <PERSON>", "DARKSTONE_PERIAPT.description": "Whenever you obtain a [red]Curse[/red], raise your Max HP by [blue]{MaxHp}[/blue].", "DARKSTONE_PERIAPT.flavor": "This stone draws power from dark energy, converting it into vitality for the wearer.", "DARKSTONE_PERIAPT.title": "Darkstone Periapt", "DARLEEJING_TEA.description": "At the start of the next {Combats:plural:combat|[blue]{}[/blue] combats}, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "DARLEEJING_TEA.flavor": "PLACEHOLDER: A tea from outside the Spire.", "DARLEEJING_TEA.title": "Darleejing Tea", "DATA_DISK.description": "Start each combat with [blue]{Focus}[/blue] [gold]Focus[/gold].", "DATA_DISK.flavor": "This disk contains precious data on birds and snakes.", "DATA_DISK.title": "Data Disk", "DAUGHTER_OF_THE_WIND.description": "Whenever you play an [gold]Attack[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "DAUGHTER_OF_THE_WIND.flavor": "PLACEHOLDER: \"You should be happy I chose you!\"", "DAUGHTER_OF_THE_WIND.title": "Daughter of the Wind", "DEMON_DISCOVERY.description": "See [blue]{Cards}[/blue] [gold]Ironclad[/gold] cards. Choose any number of them to add to your [gold]Deck[/gold].", "DEMON_DISCOVERY.flavor": "PLACEHOLDER", "DEMON_DISCOVERY.selectionScreenPrompt": "Select Any Number of Cards to Add to Your Deck.", "DEMON_DISCOVERY.title": "Demon Discovery", "DEPRECATED_RELIC.description": "This relic is no longer in the game. It's just here for legacy purposes.", "DEPRECATED_RELIC.flavor": "Wow, you found a relic that doesn't exist anymore!", "DEPRECATED_RELIC.title": "Deprecated Relic", "DOLLYS_MIRROR.description": "Upon pickup, obtain an additional copy of a card in your [gold]Deck[/gold].", "DOLLYS_MIRROR.flavor": "\"I look funny in this.\"", "DOLLYS_MIRROR.selectionScreenPrompt": "Select [blue]1[/blue] card to [gold]Copy[/gold]", "DOLLYS_MIRROR.title": "Dolly's Mirror", "DRAGON_FRUIT.description": "Whenever you gain Gold, raise your Max HP by [blue]{MaxHp}[/blue].", "DRAGON_FRUIT.flavor": "Contrary to popular belief, this does not come from dragons.", "DRAGON_FRUIT.title": "Dragon Fruit", "DREAM_CATCHER.description": "Whenever you rest, you may add a card to your [gold]Deck[/gold].", "DREAM_CATCHER.flavor": "The northern tribes would often use dream catchers at night, believing they led to self improvement.", "DREAM_CATCHER.title": "Dream Catcher", "DUSTY_TOME.description": "Upon pickup, obtain {AncientCard.StringValue:cond:[gold]{}[/gold]|an [gold]Ancient Card[/gold]}.", "DUSTY_TOME.eventDescription": "Obtain {AncientCard.StringValue:cond:[gold]{}[/gold]|an [gold]Ancient Card[/gold]}.", "DUSTY_TOME.flavor": "PLACEHOLDER", "DUSTY_TOME.title": "<PERSON>", "DUST_BUNNY.description": "Whenever you shuffle your [gold]Draw Pile[/gold], add a [gold]Soot[/gold] to your [gold]Draw Pile[/gold].", "DUST_BUNNY.flavor": "PLACEHOLDER: \"Bad for allergies.\"", "DUST_BUNNY.title": "<PERSON> Bunny", "DYBBUK_CUBE.description": "Each combat, the first time you play a card that [gold]Debuffs[/gold] an enemy, double its effect.", "DYBBUK_CUBE.flavor": "PLACEHOLDER: The dislocated soul of a dead person, trapped for all eternity.", "DYBBUK_CUBE.title": "Dybbuk Cube", "ECTOPLASM.description": "You can no longer gain [gold]Gold[/gold]. Gain {Energy:energyIcons()} at the start of your turn.\n", "ECTOPLASM.flavor": "This blob of slime and energy seems to pulse with life.", "ECTOPLASM.title": "Ectoplasm", "ELECTRIFIED_SHARD.description": "Upon pickup, enchant a skill with [gold]Imbued[/gold].", "ELECTRIFIED_SHARD.eventDescription": "Enchant a skill with [gold]Imbued[/gold].", "ELECTRIFIED_SHARD.flavor": "PLACEHOLDER: Do not taste it.", "ELECTRIFIED_SHARD.title": "Electrified <PERSON>hard", "EMBER_TEA.description": "At the start of the next {Combats:plural:combat|[blue]{}[/blue] combats}, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "EMBER_TEA.flavor": "A boiling explosively hot tea full of soot.", "EMBER_TEA.title": "Ember Tea", "EMOTION_CHIP.description": "If you lost HP during the previous turn, trigger the passive ability of all Orbs at the start of your turn.", "EMOTION_CHIP.flavor": "...<3...?", "EMOTION_CHIP.title": "Emotion Chip", "EMPTY_CAGE.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "EMPTY_CAGE.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "EMPTY_CAGE.flavor": "\"How unusual to cage that which you worship\" \n-<PERSON><PERSON><PERSON><PERSON>", "EMPTY_CAGE.title": "Empty Cage", "ENDLESS_APPETITE.description": "Whenever you [gold]Exhaust[/gold] a card, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "ENDLESS_APPETITE.flavor": "Placeholder: Must... eat", "ENDLESS_APPETITE.title": "Endless Appetite", "ENORMOUS_RING.description": "Whenever [gold]Summon[/gold] revives [gold]Osty[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "ENORMOUS_RING.flavor": "PLACEHOLDER", "ENORMOUS_RING.title": "Enormous Ring", "ETERNAL_FEATHER.description": "For every {Cards:plural:card|[blue]{}[/blue] cards} in your [gold]Deck[/gold], heal [green]{Heal}[/green] HP whenever you enter a Rest Site.", "ETERNAL_FEATHER.flavor": "This feather appears to be completely indestructible. What bird does this possibly come from?", "ETERNAL_FEATHER.title": "Eternal Feather", "FABLE.description": "Whenever you add a card to your [gold]Deck[/gold], add one additional copy.", "FABLE.flavor": "PLACEHOLDER: \"...\"", "FABLE.title": "Fable", "FENCING_MANUAL.description": "Whenever you play [gold]Sovereign Blade[/gold], [gold]Forge[/gold] [blue]{Forge}[/blue].", "FENCING_MANUAL.flavor": "PLACEHOLDER", "FENCING_MANUAL.title": "Fencing Manual", "FRAGRANT_MUSHROOM.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random [gold]{Cards:plural:card|cards}[/gold].", "FRAGRANT_MUSHROOM.flavor": "PLACEHOLDER", "FRAGRANT_MUSHROOM.title": "Fragrant Mushroom", "FROZEN_EGG.description": "Whenever you add a [gold]Power[/gold] card into your [gold]Deck[/gold], [gold]Upgrade[/gold] it.", "FROZEN_EGG.flavor": "The egg lies inert and frozen, never to hatch.", "FROZEN_EGG.title": "Frozen Egg", "GALACTIC_DUST.description": "At the start of each combat, gain {Stars:starIcons()}.", "GALACTIC_DUST.flavor": "PLACEHOLDER", "GALACTIC_DUST.title": "Galactic Dust", "GAMBLING_CHIP.description": "At the start of each combat, discard any number of cards then draw that many.", "GAMBLING_CHIP.flavor": "You can see a small inscription on one side. It reads: \"Bear's Lucky Chip!\"", "GAMBLING_CHIP.selectionScreenPrompt": "Choose Any Number of Cards to Replace", "GAMBLING_CHIP.title": "Gambling Chip", "GEAR_DISCOVERY.description": "See [blue]{Cards}[/blue] [gold]Defect[/gold] cards. Choose any number of them to add to your [gold]Deck[/gold].", "GEAR_DISCOVERY.flavor": "PLACEHOLDER", "GEAR_DISCOVERY.selectionScreenPrompt": "Select Any Number of Cards to Add to Your Deck.", "GEAR_DISCOVERY.title": "Gear Discovery", "GHOST_SEED.description": "[gold]Strikes[/gold] and [gold]Defends[/gold] gain [gold]Ethereal[/gold].", "GHOST_SEED.flavor": "PLACEHOLDER", "GHOST_SEED.title": "<PERSON> Seed", "GIFT_BOX.description": "Upon pickup, obtain [blue]{Relics}[/blue] random [gold]{Relics:plural:Relic|Relics}[/gold]. Add an additional [gold]Strike[/gold] and [gold]Defend[/gold] to your [gold]Deck[/gold].", "GIFT_BOX.eventDescription": "Obtain [blue]{Relics}[/blue] random [gold]{Relics:plural:Relic|Relics}[/gold]. Add an additional [gold]Strike[/gold] and [gold]Defend[/gold] to your [gold]Deck[/gold].", "GIFT_BOX.flavor": "PLACEHOLDER", "GIFT_BOX.title": "Gift Box", "GIRYA.description": "You can now gain [gold]Strength[/gold] at [gold]Rest Sites[/gold]. ([blue]3[/blue] times max)", "GIRYA.flavor": "This Girya is unfathomably heavy. You could train with this to get significantly stronger.", "GIRYA.title": "<PERSON><PERSON><PERSON>", "GLASS_EYE.description": "Upon pickup, obtain [blue]2[/blue] [gold]Common[/gold] cards, [blue]2[/blue] [gold]Uncommon[/gold] cards, and [blue]1[/blue] [gold]Rare[/gold] card.", "GLASS_EYE.flavor": "PLACEHOLDER", "GLASS_EYE.title": "Glass Eye", "GLOOPY_EYE.description": "The first time each combat you end your turn without playing cards, [gold]Exhaust[/gold] your hand, and take an extra turn.", "GLOOPY_EYE.flavor": "<PERSON><PERSON><PERSON> gazes back at you.", "GLOOPY_EYE.title": "Gloopy Eye", "GLOWING_ORB.description": "Start the next Boss encounter with an additional {Energy:energyIcons()}.", "GLOWING_ORB.flavor": "PLACEHOLDER: Sorry defect", "GLOWING_ORB.title": "Glowing Orb", "GOLDEN_COMPASS.description": "Replaces the [gold]Act[/gold] [blue]2[/blue] Map with a single special path.", "GOLDEN_COMPASS.flavor": "\"Points in the direction of you new fate.\"", "GOLDEN_COMPASS.title": "Golden Compass", "GOLDEN_RING.description": "Enchant an [gold]Attack[/gold] with [blue]{Momentum}[/blue] [gold]Momentum[/gold].", "GOLDEN_RING.flavor": "PLACEHOLDER: it is gold and it is a ring", "GOLDEN_RING.title": "Golden Ring", "GOLD_PLATED_CABLES.description": "Your rightmost Orb triggers its passive an additional time.", "GOLD_PLATED_CABLES.flavor": "\"Interesting! Even automatons are affected by placebo.\" -<PERSON><PERSON><PERSON><PERSON>", "GOLD_PLATED_CABLES.title": "Gold-Plated Cables", "GREMLIN_HORN.description": "Whenever an enemy dies, gain {Energy:energyIcons()} and draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "GREMLIN_HORN.flavor": "\"Gremlin Nobs are capable of growing until the day they die. Remarkable.\" - <PERSON><PERSON><PERSON><PERSON>", "GREMLIN_HORN.title": "Gremlin Horn", "HAND_DRILL.description": "Whenever you break an enemy's [gold]Block[/gold], apply [blue]{Vulnerable}[/blue] [gold]Vulnerable[/gold].", "HAND_DRILL.flavor": "\"Spirals are dangerous.\"", "HAND_DRILL.title": "<PERSON> Drill", "HAPPY_FLOWER.description": "Every [blue]{Turns}[/blue] {Turns:plural:turn|turns}, gain {Energy:energyIcons()}.", "HAPPY_FLOWER.flavor": "This unceasingly joyous plant is a popular novelty item among nobles.", "HAPPY_FLOWER.title": "<PERSON> Flower", "HEADHUNTER.description": "Whenever an enemy dies, gain all the [gold]Strength[/gold] and [gold]Dexterity[/gold] it had.", "HEADHUNTER.flavor": "PLACEHOLDER: \"A man's soul rules from a cavern of bone, learns and judges through flesh-born windows. The heart is meat. The head is where the Man is.\" - <PERSON><PERSON><PERSON>, Advisor to <PERSON><PERSON>", "HEADHUNTER.title": "Headhunter", "HEART_PIECE.description": "You cannot lose more than 20 HP in a single turn.", "HEART_PIECE.flavor": "PLACEHOLDER: Should have gotten the stamina instead.", "HEART_PIECE.title": "Heart Piece", "HISTORY_COURSE.description": "At the start of your turn, play a copy of your last played card.", "HISTORY_COURSE.flavor": "PLCAEHOLDER: The title on the book reads \"Lore of the Spire\".", "HISTORY_COURSE.title": "History Course", "HORN_CLEAT.description": "At the start of your 2nd turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "HORN_CLEAT.flavor": "Pleasant to hold in the hand. What was it for?", "HORN_CLEAT.title": "<PERSON>", "HUNGERING_BELLY.description": "Whenever a non-minion enemy dies, heal [green]{Heal}[/green] HP.", "HUNGERING_BELLY.flavor": "PLACEHOLDER", "HUNGERING_BELLY.title": "Hungering Belly", "HUNGERING_PORTRAIT.description": "At the start of your turn, [gold]Exhaust[/gold] the top card of your [gold]Deck[/gold] and gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "HUNGERING_PORTRAIT.flavor": "PLACEHOLDER", "HUNGERING_PORTRAIT.title": "Hungering Portrait", "ICE_CREAM.description": "Energy is now conserved between turns.", "ICE_CREAM.flavor": "Delicious!", "ICE_CREAM.title": "Ice Cream", "ICE_CUBE.description": "The first time you play a Power each combat, gain [blue]{Block}[/blue] [gold]Block[/gold].", "ICE_CUBE.flavor": "PLACEHOLDER: Straight Outta Compton.", "ICE_CUBE.title": "Ice Cube", "ILLUSORY_WALL.description": "If you did not play any [gold]Attacks[/gold] during your turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "ILLUSORY_WALL.flavor": "PLACEHOLDER", "ILLUSORY_WALL.title": "Illusory Wall", "IMMORTAL_SOUL.description": "You cannot die from [gold]Doom[/gold].", "IMMORTAL_SOUL.flavor": "PLACEHOLDER", "IMMORTAL_SOUL.title": "Immortal Soul", "INFUSED_CORE.description": "At the start of each combat, [gold]Channel[/gold] [blue]{Lightning}[/blue] [gold]Lightning[/gold].", "INFUSED_CORE.flavor": "PLACEHOLDER", "INFUSED_CORE.title": "Infused Core", "INK_BOTTLE.description": "Every time you play [blue]{CardThreshold}[/blue] {CardThreshold:plural:card|cards}, draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "INK_BOTTLE.flavor": "Once exhausted, appears to refill itself in a different color.", "INK_BOTTLE.title": "Ink Bottle", "INTIMIDATING_HELMET.description": "Whenever you play a card that costs {Energy:energyIcons()} or more, gain [blue]{Block}[/blue] [gold]Block[/gold].", "INTIMIDATING_HELMET.flavor": "PLACEHOLDER", "INTIMIDATING_HELMET.title": "Intimidating Helmet", "JOSS_PAPER.description": "Every [blue]{ExhaustAmount}[/blue] times you [gold]Exhaust[/gold] a card, draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "JOSS_PAPER.flavor": "PLACEHOLDER: Sometimes, the dead reciprocate.", "JOSS_PAPER.title": "Joss Paper", "JUZU_BRACELET.description": "Regular enemy combats are no longer encountered in [gold]?[/gold] rooms.", "JUZU_BRACELET.flavor": "A ward against the unknown.", "JUZU_BRACELET.title": "Juzu Bracelet", "KALEIDOSCOPE.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random {Cards:plural:card|cards}.", "KALEIDOSCOPE.flavor": "PLACEHOLDER", "KALEIDOSCOPE.title": "Kaleidoscope", "KIFUDA.description": "Enchant a [gold]Skill[/gold] with [gold]Adroit[/gold].", "KIFUDA.flavor": "PLACEHOLDER: Wait, the symbols on this are gibberish!", "KIFUDA.title": "<PERSON><PERSON><PERSON>", "KUNAI.description": "Every time you play {Cards} [gold]Attacks[/gold] in a single turn, gain [blue]{Dexterity}[/blue] [gold]Dexterity[/gold].", "KUNAI.flavor": "A blade favored by assassins for its lethality at range.", "KUNAI.title": "Kunai", "KUSARIGAMA.description": "Every time you play [blue]{Cards}[/blue] [gold]Skills[/gold], you gain [blue]{Block}[/blue] [gold]Block[/gold].", "KUSARIGAMA.flavor": "PLACEHOLDER: An elegant weapon for more civilized times.", "KUSARIGAMA.title": "Ku<PERSON><PERSON><PERSON>", "LANTERN.description": "Start each combat with an additional {Energy:energyIcons()}.", "LANTERN.flavor": "An eerie lantern which illuminates only for the wielder.", "LANTERN.title": "Lantern", "LASTING_CANDY.description": "Every other combat, your card rewards gain an additional [gold]Power[/gold] card.", "LASTING_CANDY.flavor": "PLACEHOLDER: So powerful!", "LASTING_CANDY.title": "Lasting <PERSON>", "LATTICE_OF_EYES.description": "[gold]Elites[/gold] drop an additional [gold]Rare[/gold] card reward.", "LATTICE_OF_EYES.flavor": "PLACEHOLDER: They're looking.", "LATTICE_OF_EYES.title": "La<PERSON>ce of Eyes", "LEAD_PAPERWEIGHT.description": "Upon pickup, choose [blue]1[/blue] of [blue]3[/blue] [gold]Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "LEAD_PAPERWEIGHT.eventDescription": "Choose [blue]1[/blue] of [blue]3[/blue] [gold]Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "LEAD_PAPERWEIGHT.flavor": "PLACEHOLDER", "LEAD_PAPERWEIGHT.title": "Lead Paperweight", "LEES_WAFFLE.description": "Raise your Max HP by [blue]{MaxHp}[/blue] and heal all of your HP.", "LEES_WAFFLE.flavor": "\"Tastiest treat you will find in all the Spire! Baked today just for you.\"", "LEES_WAFFLE.title": "<PERSON>'s Waffle", "LETTER_OPENER.description": "Every time you play {Cards} [gold]Skills[/gold] in a single turn, deal {Damage} damage to all enemies.", "LETTER_OPENER.flavor": "Unnaturally sharp.", "LETTER_OPENER.title": "Letter Opener", "LICH_DISCOVERY.description": "See [blue]{Cards}[/blue] [gold]Necrobinder[/gold] cards. Choose any number of them to add to your [gold]Deck[/gold].", "LICH_DISCOVERY.flavor": "PLACEHOLDER", "LICH_DISCOVERY.selectionScreenPrompt": "Select Any Number of Cards to Add to Your Deck.", "LICH_DISCOVERY.title": "Lich Discovery", "LIZARD_TAIL.description": "When you would die, heal to [green]{Heal}%[/green] of your Max HP instead (works once).", "LIZARD_TAIL.flavor": "A fake tail to trick enemies during combat.", "LIZARD_TAIL.title": "<PERSON>rd <PERSON>l", "LOOMING_FRUIT.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "LOOMING_FRUIT.eventDescription": "Raise your Max HP by [blue]{MaxHp}[/blue].", "LOOMING_FRUIT.flavor": "What a bountiful harvest (Placeholder).", "LOOMING_FRUIT.title": "Looming Fruit", "LUCKY_FYSH.description": "Whenever you add a card to your [gold]Deck[/gold], gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "LUCKY_FYSH.flavor": "PLACEHOLDER: The hand waves, powered by a mysterious force.", "LUCKY_FYSH.title": "<PERSON>", "MAGIC_POT.description": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. After each combat, randomly add [blue]1[/blue] back [gold]Upgraded[/gold].{CardTitles.StringValue:cond:\n\nCards in the Pot:\n{}|}", "MAGIC_POT.flavor": "PLACEHOLDER: A black metal pot that shimmers an unnatural yellow by candlelight.", "MAGIC_POT.title": "Magic Pot", "MANGO.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "MANGO.flavor": "The most coveted forgotten fruit. Impeccably preserved with no signs of Spireblight.", "MANGO.title": "Mango", "MASTERS_TEACHINGS.description": "Whenever you play a card that costs {energyPrefix:energyIcons(3)} or more, gain {Energy:energyIcons()}.", "MASTERS_TEACHINGS.flavor": "PLACEHOLDER", "MASTERS_TEACHINGS.title": "Master's Teachings", "MATCHBOX.description": "Start each combat with an additional {Energy:energyIcons()}.", "MATCHBOX.flavor": "PLACEHOLDER: Burns bright and hot.", "MATCHBOX.title": "Matchbox", "MAW_BANK.description": "Whenever you climb a floor, gain [blue]{Gold}[/blue] [gold]Gold[/gold]. No longer works when you spend any [gold]Gold[/gold] at the shop.", "MAW_BANK.flavor": "Surprisingly popular, despite maw attacks being a regular occurrence.", "MAW_BANK.title": "Maw Bank", "MEAL_TICKET.description": "Whenever you enter a shop room, heal [green]{Heal}[/green] HP.", "MEAL_TICKET.flavor": "\"Complimentary meatballs with every visit!\"", "MEAL_TICKET.title": "Meal Ticket", "MEAT_ON_THE_BONE.description": "If your HP is at or below [blue]{HpT<PERSON>esh<PERSON>}%[/blue] at the end of combat, heal [green]{Heal}[/green] HP.", "MEAT_ON_THE_BONE.flavor": "The meat keeps replenishing, never seeming to fully run out.", "MEAT_ON_THE_BONE.title": "Meat on the Bone", "MEDICAL_KIT.description": "[gold]Status[/gold] cards can now be played. Playing a Status will [gold]Exhaust[/gold] the card.", "MEDICAL_KIT.flavor": "\"Has everything you need! Anti-itch, anti-burn, anti-venom, and more!\"", "MEDICAL_KIT.title": "Medical Kit", "MEMBERSHIP_CARD.description": "[blue]{Discount}%[/blue] discount on all products!", "MEMBERSHIP_CARD.flavor": "\"Bonus membership offer for my most valuable customers!\"", "MEMBERSHIP_CARD.title": "Membership Card", "MERCHANTS_ROBES.description": "Enemies no longer drop card rewards. Cards in Shops are free.", "MERCHANTS_ROBES.flavor": "PLACEHOLDER", "MERCHANTS_ROBES.title": "Merchant's Robes", "MERCURY_HOURGLASS.description": "At the start of your turn, deal [blue]{Damage}[/blue] damage to all enemies.", "MERCURY_HOURGLASS.flavor": "An enchanted hourglass that endlessly drips.", "MERCURY_HOURGLASS.title": "Mercury Hourglass", "MOLTEN_EGG.description": "Whenever you add an [gold]Attack[/gold] card to your [gold]Deck[/gold], [gold]Upgrade[/gold] it.", "MOLTEN_EGG.flavor": "The egg of a Phoenix. It glows red hot with a simmering lava.", "MOLTEN_EGG.title": "Molten Egg", "MR_STRUGGLES.description": "At the start of your turn, deal damage equal to the turn number to all enemies.", "MR_STRUGGLES.flavor": "PLACEHOLDER: \"We are going to uncover the most delightful stories together!\"", "MR_STRUGGLES.title": "Mr. <PERSON>", "MUMMIFIED_HAND.description": "Whenever you play a [gold]Power[/gold], a random card in your [gold]Hand[/gold] is free to play that turn.", "MUMMIFIED_HAND.flavor": "Frequently twitches, especially when your pulse is high.", "MUMMIFIED_HAND.title": "Mummified Hand", "MYSTERIOUS_COCOON.description": "Upon gaining [blue]{Experience}[/blue] experience, transform [gold]Training Strike[/gold] into [gold]Impale[/gold].", "MYSTERIOUS_COCOON.flavor": "PLACEHOLDER: Shaped like a bug.", "MYSTERIOUS_COCOON.title": "Mysterious Cocoon", "MYSTERY_MACHINE.description": "Add [blue]{Unknown}[/blue] extra [gold]?[/gold]s to each map.", "MYSTERY_MACHINE.flavor": "PLACEHOLDER", "MYSTERY_MACHINE.title": "Mystery Machine", "MY_MANTLE.description": "For every [blue]{Stars}[/blue] [gold]{Stars:plural:Star|Stars}[/gold] spent, gain [blue]{Block}[/blue] [gold]Block[/gold].", "MY_MANTLE.flavor": "PLACEHOLDER", "MY_MANTLE.title": "My Mantle", "NEOWS_NOTE.description": "Gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "NEOWS_NOTE.flavor": "Good things come to those who check their mail.", "NEOWS_NOTE.title": "<PERSON><PERSON>'s Note", "NINJA_SCROLL.description": "At the start of each combat, add [blue]{Shivs}[/blue] [gold]{Shivs:plural:Shiv|Shivs}[/gold] to your [gold]Hand[/gold].", "NINJA_SCROLL.flavor": "Contains the secrets of assassination.", "NINJA_SCROLL.title": "<PERSON>", "NOBLE_DISCOVERY.description": "See [blue]{Cards}[/blue] [gold]Regent[/gold] cards. Choose any number of them to add to your [gold]Deck[/gold].", "NOBLE_DISCOVERY.flavor": "PLACEHOLDER", "NOBLE_DISCOVERY.selectionScreenPrompt": "Select Any Number of Cards to Add to Your Deck.", "NOBLE_DISCOVERY.title": "Noble Discovery", "NUNCHAKU.description": "Every time you play [blue]{Cards}[/blue] [gold]Attacks[/gold], gain {Energy:energyIcons()}.", "NUNCHAKU.flavor": "A good training tool. Improves the posture and agility of the wielder.", "NUNCHAKU.title": "<PERSON><PERSON><PERSON><PERSON>", "NYE_ESSENCE.description": "At the start of your 3rd turn, gain {Energy:energyIcons()}.", "NYE_ESSENCE.flavor": "PLACEHOLDER: The essence of New Year's Eve.", "NYE_ESSENCE.title": "<PERSON><PERSON>", "ODDLY_HEAVY_STONE.description": "The first time you gain [gold]Block[/gold] from a card each turn, double the amount gained.", "ODDLY_HEAVY_STONE.flavor": "PLACEHOLDER", "ODDLY_HEAVY_STONE.title": "Oddly Heavy Stone", "ODDLY_SMOOTH_STONE.description": "Start each combat with [blue]{Dexter<PERSON>}[/blue] [gold]Dexterity[/gold].", "ODDLY_SMOOTH_STONE.flavor": "You have never seen something so smooth and pristine. This must be the work of the Ancients.", "ODDLY_SMOOTH_STONE.title": "<PERSON><PERSON>", "OLD_COIN.description": "Upon pickup, gain [blue]{Gold}[/blue] Gold.", "OLD_COIN.flavor": "Unique coins are highly valued by Merchants for their historical value and rare metallic composition.", "OLD_COIN.title": "Old Coin", "ORACLE_BONE.description": "The first card you play each combat is played an extra time.", "ORACLE_BONE.flavor": "PLACEHOLDER: Every time you ask the bone for a fortune, it always says that the first card you play each combat will be played an extra time.", "ORACLE_BONE.title": "Oracle Bone", "ORICHALCUM.description": "If you end your turn without [gold]Block[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "ORICHALCUM.flavor": "A green tinted metal of an unknown origin. Seemingly indestructible.", "ORICHALCUM.title": "Orichalcum", "ORNAMENTAL_FAN.description": "Every time you play [blue]{Cards}[/blue] [gold]Attacks[/gold] in a single turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "ORNAMENTAL_FAN.flavor": "The fan seems to extend and harden as blood is spilled.", "ORNAMENTAL_FAN.title": "Ornamental Fan", "ORNATE_RELIQUARY.description": "Whenever a non-Minion enemy dies to [gold]Doom[/gold], heal [blue]{Heal}[/blue] HP.", "ORNATE_RELIQUARY.flavor": "PLACEHOLDER", "ORNATE_RELIQUARY.title": "Ornate Reliquary", "ORRERY.description": "Upon pickup, choose and add [blue]{Cards}[/blue] {Cards:plural:card|cards} to your [gold]Deck[/gold].", "ORRERY.flavor": "PLACEHOLDER", "ORRERY.title": "<PERSON><PERSON><PERSON>", "OVERSIZED_NAIL.description": "Whenever you add an [gold]Attack[/gold] into your [gold]Deck[/gold], [gold]Enchant[/gold] it with [blue]{SharpAmount}[/blue] [purple]Sharp[/purple].", "OVERSIZED_NAIL.flavor": "PLACEHOLDER: When you have a nail, everything - you know the rest.", "OVERSIZED_NAIL.title": "Oversized Nail", "PAELS_CLAW.description": "Whenever you [gold]Rest[/gold], [gold]Upgrade[/gold] [blue]{Cards}[/blue] random {Cards:plural:card|cards} in your [gold]Deck[/gold].", "PAELS_CLAW.flavor": "PLACEHOLDER: Somehow still both soft and sharp at the same time.", "PAELS_CLAW.title": "<PERSON><PERSON>'s Claw", "PAELS_FLESH.description": "Gain an additional {Energy:energyIcons()} at the start of your [blue]3rd[/blue] turn, and every turn after that.", "PAELS_FLESH.flavor": "PLACEHOLDER.", "PAELS_FLESH.title": "<PERSON><PERSON>'s Flesh", "PAELS_TEARS.description": "If you end your turn with unspent {energyPrefix:energyIcons(1)}, gain an additional {Energy:energyIcons()} next turn.", "PAELS_TEARS.flavor": "PLACEHOLDER", "PAELS_TEARS.title": "<PERSON><PERSON>'s Tears", "PAELS_WING.description": "When adding cards to your [gold]Deck[/gold], you may sacrifice the choice to <PERSON><PERSON> instead. Every {Sacrifices:plural:sacrifice|[blue]{}[/blue] sacrifices}, obtain a random [gold]Relic[/gold].", "PAELS_WING.flavor": "PLACEHOLDER: Somehow still both soft and sharp at the same time.", "PAELS_WING.title": "Pa<PERSON>'s Wing", "PANDORAS_BOX.description": "[gold]Transform[/gold] all [gold]Strikes[/gold] and [gold]Defends[/gold].", "PANDORAS_BOX.flavor": "PLACEHOLDER", "PANDORAS_BOX.infoText": "Cards transformed by [gold]Pandora's Box[/gold].", "PANDORAS_BOX.title": "Pandora's Box", "PANTOGRAPH.description": "At the start of boss combats, heal [green]{Heal}[/green] HP.", "PANTOGRAPH.flavor": "\"Solid foundations are not accidental. Tools for planning are a must.\" - The Architect", "PANTOGRAPH.title": "Pantograph", "PAPER_KRANE.description": "Enemies with [gold]Weak[/gold] deal [blue]40%[/blue] less damage to you rather than [blue]25%[/blue].", "PAPER_KRANE.flavor": "An origami of a creature from a past age.", "PAPER_KRANE.title": "Paper Krane", "PAPER_PHROG.description": "Enemies with [gold]Vulnerable[/gold] take [blue]75%[/blue] more damage rather than [blue]50%[/blue].", "PAPER_PHROG.flavor": "The paper continually folds and unfolds itself into the shape of a small creature.", "PAPER_PHROG.title": "Paper Phrog", "PEAR.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "PEAR.flavor": "A common fruit before the Spireblight.", "PEAR.title": "Pear", "PEIN_HAMMER.description": "The first hand you draw each combat is upgraded.", "PEIN_HAMMER.flavor": "PLACEHOLDER: p e i n", "PEIN_HAMMER.title": "<PERSON><PERSON>", "PENDULUM.description": "Whenever you shuffle your [gold]Draw Pile[/gold], draw a card.", "PENDULUM.flavor": "PLACEHOLDER: You feel yourself getting very sleepy.", "PENDULUM.title": "Pen<PERSON><PERSON>", "PEN_NIB.description": "Every [blue]10th[/blue] [gold]Attack[/gold] you play deals double damage.", "PEN_NIB.flavor": "Holding the nib, you can see everyone ever slain by a previous owner of the pen. A violent history.", "PEN_NIB.title": "<PERSON>", "PERPETUAL_COIN.description": "Upon pickup, gain [blue]{Gold}[/blue] [gold]Gold[/gold]. Merchants no longer appear in [gold]?[/gold] rooms.", "PERPETUAL_COIN.flavor": "PLACEHOLDER: The gold piece burns in your hand.", "PERPETUAL_COIN.title": "Perpetual Coin", "PETRIFIED_TOAD.description": "At the start of combat, procure a [gold]Potion-Shaped Rock[/gold].", "PETRIFIED_TOAD.flavor": "PLACEHOLDER", "PETRIFIED_TOAD.title": "Petrified Toad", "PHILOSOPHERS_STONE.description": "Gain {Energy:energyIcons()} at the start of your turn. All enemies start combat with [blue]{Strength}[/blue] Strength.", "PHILOSOPHERS_STONE.flavor": "Raw energy emanates from the stone, empowering all nearby.", "PHILOSOPHERS_STONE.title": "Philoso<PERSON>'s Stone", "PHYLACTERY_UNBOUND.description": "At the start of combat, [gold]Summon[/gold] [blue]{<PERSON>mm<PERSON>}[/blue].", "PHYLACTERY_UNBOUND.flavor": "PLACEHOLDER", "PHYLACTERY_UNBOUND.title": "Phylactery Unbound", "PITCH_BLACK_OIL.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "PITCH_BLACK_OIL.eventDescription": "[gold]Upgrade[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "PITCH_BLACK_OIL.flavor": "PLACEHOLDER: Has a very nice fragrance. Save it for a special occasion.", "PITCH_BLACK_OIL.selectionScreenPrompt": "Select [blue]{Cards}[/blue] {Cards:plural:card|cards} to [gold]Upgrade[/gold]", "PITCH_BLACK_OIL.title": "Pitch Black Oil", "PLANISPHERE.description": "Whenever you enter a [gold]?[/gold] room, heal [green]{Heal}[/green] HP.", "PLANISPHERE.flavor": "PLACEHOLDER: A map of the stars.", "PLANISPHERE.title": "Planisphere", "POCKETWATCH.description": "Whenever you play [blue]{CardThreshold}[/blue] or less {CardThreshold:plural:card|cards} during your turn, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards} at the start of your next turn.", "POCKETWATCH.flavor": "The hands seem stuck on the 3 o'clock position.", "POCKETWATCH.title": "Pocketwatch", "POTION_BELT.description": "Upon pickup, gain [blue]{PotionSlots}[/blue] potion {PotionSlots:plural:slot|slots}.", "POTION_BELT.flavor": "I can hold more Potions using this belt!", "POTION_BELT.title": "Potion Belt", "PRAYER_WHEEL.description": "Normal enemies drop an additional card reward.", "PRAYER_WHEEL.flavor": "The wheel continues to spin, never stopping.", "PRAYER_WHEEL.title": "Prayer Wheel", "PRISMATIC_SHARD.description": "Card rewards now contain cards from other colors.", "PRISMATIC_SHARD.flavor": "Looking through the shard, you are able to see entirely new perspectives.", "PRISMATIC_SHARD.title": "Prismatic Shard", "PULSING_CORE.description": "Every [blue]{Turns}[/blue] {Turns:plural:turn|turns}, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "PULSING_CORE.flavor": "Placeholder", "PULSING_CORE.title": "Pulsing Core", "RAINBOW_RING.description": "When you play an Attack, Skill, and Power in a single turn, gain [blue]{Strength}[/blue] [gold]Strength[/gold] and [blue]{Dexter<PERSON>}[/blue] [gold]Dexterity[/gold].", "RAINBOW_RING.flavor": "PLACEHOLDER", "RAINBOW_RING.title": "Rainbow Ring", "RAZOR_TOOTH.description": "Every time you play a card, [gold]Upgrade[/gold] it for the remainder of combat.", "RAZOR_TOOTH.flavor": "PLACEHOLDER: pointy.", "RAZOR_TOOTH.title": "<PERSON><PERSON>", "RECURSIVE_CORE.description": "Upon pickup, replace {StarterRelic.StringValue:cond:[gold]{StarterRelic}[/gold] with [gold]{UpgradedRelic}[/gold]|your starter relic with an ancient version}.", "RECURSIVE_CORE.eventDescription": "Replace {StarterRelic.StringValue:cond:[gold]{StarterRelic}[/gold] with [gold]{UpgradedRelic}[/gold]|your starter relic with an ancient version}.", "RECURSIVE_CORE.flavor": "PLACEHOLDER", "RECURSIVE_CORE.title": "Recursive Core", "RED_MASK.description": "At the start of each combat, apply [blue]{Weak}[/blue] [gold]Weak[/gold] to ALL enemies.", "RED_MASK.flavor": "You feel like someone paid too much for this.", "RED_MASK.title": "Red Mask", "RED_SKULL.description": "While your HP is at or below [blue]{HpThreshold}%[/blue], you have [blue]{Strength}[/blue] additional [gold]Strength[/gold].", "RED_SKULL.flavor": "A small skull covered in ornamental paint.", "RED_SKULL.title": "Red Skull", "REGAL_PILLOW.description": "Whenever you [gold]Rest[/gold], heal an additional [blue]{Heal}[/blue] HP.", "REGAL_PILLOW.flavor": "Now you can get a proper night's rest.", "REGAL_PILLOW.title": "Regal Pillow", "REPLICATOR.description": "Upon pickup, enchant a card with [purple]Cloning Capsule[/purple].", "REPLICATOR.eventDescription": "Enchant a card with [purple]Cloning Capsule[/purple].", "REPLICATOR.flavor": "PLACEHOLDER", "REPLICATOR.title": "Replicator", "REPTILE_TRINKET.description": "Whenever you use a potion, gain [blue]{Strength}[/blue] [gold]Strength[/gold] this turn.", "REPTILE_TRINKET.flavor": "PLACEHOLDER: You feel like it's looking at you.", "REPTILE_TRINKET.title": "Reptile Trinket", "RETRACTABLE_CLAWS.description": "Upon pickup, [gold]Transform[/gold] up to [blue]{Cards}[/blue] {Cards:plural:card|cards} into [gold]Maul[/gold].", "RETRACTABLE_CLAWS.eventDescription": "[gold]Transform[/gold] up to [blue]{Cards}[/blue] {Cards:plural:card|cards} into [gold]Maul[/gold].", "RETRACTABLE_CLAWS.flavor": "PLACEHOLDER", "RETRACTABLE_CLAWS.selectionScreenPrompt": "Select Cards to Transform into Maul.", "RETRACTABLE_CLAWS.title": "Retractable Claws", "RINGING_TRIANGLE.description": "[gold]Retain[/gold] your hand on the first turn of combat.", "RINGING_TRIANGLE.flavor": "PLACEHOLDER", "RINGING_TRIANGLE.title": "Ringing Triangle", "RING_OF_THE_DRAKE.description": "At the start of your first {Turns:plural:turn|[blue]{}[/blue] turns}, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "RING_OF_THE_DRAKE.flavor": "PLACEHOLDER: Made from a fossilized drake. Represents great skill as a huntress.", "RING_OF_THE_DRAKE.title": "Ring of the Drake", "RING_OF_THE_SNAKE.description": "At the start of each combat, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "RING_OF_THE_SNAKE.flavor": "Made from a fossilized snake. Represents great skill as a huntress.", "RING_OF_THE_SNAKE.title": "Ring of the Snake", "ROLLING_SPHERE.description": "Whenever you play a [gold]Power[/gold], draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "ROLLING_SPHERE.flavor": "Placeholder", "ROLLING_SPHERE.title": "Rolling Sphere", "ROYAL_PERFUME.description": "At the end of your turn, gain {Stars:starIcons()}.", "ROYAL_PERFUME.flavor": "PLACEHOLDER", "ROYAL_PERFUME.title": "Royal Perfume", "ROYAL_POISON.description": "At the start of each combat, lose [blue]{Damage}[/blue] HP.", "ROYAL_POISON.flavor": "PLACEHOLDER: it tastes like... burning...", "ROYAL_POISON.title": "Royal Poison", "ROYAL_STAMP.description": "Upon pickup, choose an [gold]Attack[/gold] or [gold]Skill[/gold] in your [gold]Deck[/gold] to enchant with [purple]{Enchantment}[/purple].", "ROYAL_STAMP.flavor": "PLACEHOLDER: Certified Approval from Regent Sr.", "ROYAL_STAMP.title": "Royal Stamp", "RUBY_EARRINGS.description": "All combat card rewards contain one card of each Rarity.", "RUBY_EARRINGS.flavor": "PLACEHOLDER: Very pretty earrings. They match your eyes.", "RUBY_EARRINGS.title": "<PERSON>", "RUNIC_CAPACITOR.description": "Start each combat with [blue]{Repeat}[/blue] additional [gold]Orb[/gold] {Repeat:plural:slot|slots}.", "RUNIC_CAPACITOR.flavor": "More is better.", "RUNIC_CAPACITOR.title": "Runic Capacitor", "RUNIC_PYRAMID.description": "At the end of your turn, you no longer discard your [gold]Hand[/gold].", "RUNIC_PYRAMID.flavor": "PLACEHOLDER: The runes are indecipherable.", "RUNIC_PYRAMID.title": "Runic Pyramid", "RUSTY_CHEST.description": "Upon pickup, obtain a random [gold]Relic[/gold].", "RUSTY_CHEST.eventDescription": "Obtain a random [gold]Relic[/gold].", "RUSTY_CHEST.flavor": "PLACEHOLDER", "RUSTY_CHEST.title": "<PERSON> Chest", "SCISSORS.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "SCISSORS.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "SCISSORS.flavor": "PLACEHOLDER", "SCISSORS.title": "Scissors", "SCREAMING_FLAGON.description": "If you end your turn with no cards in hand, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "SCREAMING_FLAGON.flavor": "PLACEHOLDER: If you hold it up to your ear, you hear a faint screaming.", "SCREAMING_FLAGON.title": "Screaming <PERSON><PERSON>", "SEAL_OF_GOLD.description": "Upon pickup, gain [blue]{Gold}[/blue] [gold]Gold[/gold]. You can no longer obtain [gold]Gold[/gold].", "SEAL_OF_GOLD.eventDescription": "Gain [blue]{Gold}[/blue] [gold]Gold[/gold]. You can no longer obtain [gold]Gold[/gold].", "SEAL_OF_GOLD.flavor": "PLACEHOLDER: \"The shining emblem seems to be losing its luster the longer you look at it.\"", "SEAL_OF_GOLD.title": "Seal of Gold", "SEEKING_TENTACLE.description": "You may reroll each card reward once.", "SEEKING_TENTACLE.flavor": "PLACEHOLDER", "SEEKING_TENTACLE.title": "Seeking Tentacle", "SELF_FORMING_CLAY.description": "Whenever you lose HP in combat, gain [blue]{BlockNextTurn}[/blue] [gold]Block[/gold] next turn.", "SELF_FORMING_CLAY.flavor": "\"Most curious! It appears to form itself loosely on my thoughts! Tele-clay?\" - <PERSON><PERSON><PERSON><PERSON>", "SELF_FORMING_CLAY.title": "Self-Forming Clay", "SEVERED_GILL.description": "At the start of each combat, add [blue]{Cards}[/blue] [gold]{Cards:plural:<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON>}[/gold] to your Hand.", "SEVERED_GILL.flavor": "Placeholder", "SEVERED_GILL.title": "Severed <PERSON>", "SHINY_MONOCLE.description": "Upon pickup, choose [blue]1[/blue] of [blue]3[/blue] [gold]Rare Cards[/gold] to add to your [gold]Deck[/gold].", "SHINY_MONOCLE.eventDescription": "Choose [blue]1[/blue] of [blue]3[/blue] [gold]Rare Cards[/gold] to add to your [gold]Deck[/gold].", "SHINY_MONOCLE.flavor": "PLACEHOLDER", "SHINY_MONOCLE.title": "<PERSON><PERSON>", "SHOEHORN.description": "If you end a turn with at least [blue]{Block}[/blue] [gold]Block[/gold], deal [blue]{Damage}[/blue] damage to a random enemy.", "SHOEHORN.flavor": "PLACEHOLDER: A shoehorn doesn't really fit in the Spire's lore, but somehow, it is here anyway.", "SHOEHORN.title": "<PERSON><PERSON><PERSON>", "SHOVEL.description": "You can now dig at [gold]Rest Sites[/gold] to obtain a random [gold]Relic[/gold].", "SHOVEL.flavor": "The Spire houses all number of relics from past civilizations and powerful adventurers lost to time. Time to go dig them up!", "SHOVEL.title": "<PERSON><PERSON><PERSON>", "SHURIKEN.description": "Every time you play [blue]{Cards}[/blue] Attacks in a single turn, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "SHURIKEN.flavor": "Lightweight throwing weapons. Recommend going for the eyes.", "SHURIKEN.title": "<PERSON><PERSON><PERSON>", "SILVER_CRUCIBLE.description": "The first {Cards:plural:card reward you see is|[blue]{}[/blue] card rewards you see are} [gold]Upgraded[/gold].", "SILVER_CRUCIBLE.flavor": "A metal container filled with lava.\nWARNING: Very hot.", "SILVER_CRUCIBLE.title": "Silver Crucible", "SLING_OF_COURAGE.description": "Start each Elite combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "SLING_OF_COURAGE.flavor": "\"A handy tool for dealing with particularly tough opponents.\"", "SLING_OF_COURAGE.title": "Sling of Courage", "SMOLDERING_WICK.description": "Upon pickup, add [blue]1[/blue] [gold]Brightest Flame[/gold] to your [gold]Deck[/gold].", "SMOLDERING_WICK.eventDescription": "Add [blue]1[/blue] [gold]Brightest Flame[/gold] to your [gold]Deck[/gold].", "SMOLDERING_WICK.flavor": "The fleeting flame glows bright on the ornate candle.", "SMOLDERING_WICK.title": "Smoldering Wick", "SNECKO_EYE.description": "At the start of your turn, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}. Start each combat [red]Confused[/red].", "SNECKO_EYE.flavor": "An eye of a fallen snecko. Much larger than you imagined.", "SNECKO_EYE.title": "Snecko Eye", "SNECKO_SKULL.description": "Whenever you apply [gold]Poison[/gold], apply an additional [blue]{Poison}[/blue] [gold]Poison[/gold].", "SNECKO_SKULL.flavor": "A snecko skull in pristine condition. Mysteriously clean and smooth, dirt and grime fall off inexplicably.", "SNECKO_SKULL.title": "Snecko Skull", "SOUL_BUDDY.description": "At the start of each combat, raise your Max HP by [blue]{MaxHp}[/blue].", "SOUL_BUDDY.flavor": "PLACEHOLDER", "SOUL_BUDDY.title": "Soul Buddy", "SOZU.description": "Gain {Energy:energyIcons()} at the start of each turn. You can no longer obtain potions.", "SOZU.flavor": "You notice that magical liquids seem to lose their properties when near this relic.", "SOZU.title": "Sozu", "SPECIAL_SLEEVE.description": "Upon pickup, choose an [gold]Attack[/gold] in your [gold]Deck[/gold]. Enchant it with [purple]Favored[/purple].", "SPECIAL_SLEEVE.eventDescription": "Choose an [gold]Attack[/gold] in your [gold]Deck[/gold]. Enchant it with [purple]Favored[/purple].", "SPECIAL_SLEEVE.flavor": "PLACEHOLDER", "SPECIAL_SLEEVE.title": "Special Sleeve", "SPIRALED_LENS.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. Lose [blue]{MaxHp}[/blue] max HP.", "SPIRALED_LENS.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. Lose [blue]{MaxHp}[/blue] max HP.", "SPIRALED_LENS.flavor": "PLACEHOLDER", "SPIRALED_LENS.title": "Spiraled Lens", "STARRIAN_TURBINE.description": "The first Colorless card created each combat costs [blue]0[/blue].", "STARRIAN_TURBINE.flavor": "PLACEHOLDER", "STARRIAN_TURBINE.title": "<PERSON><PERSON>", "STARRY.description": "At the start of each combat, gain {Stars:starIcons()}.", "STARRY.flavor": "PLACEHOLDER", "STARRY.title": "Starry", "STARTER_FLARE.description": "At the start of each combat, deal [blue]{Damage}[/blue] damage to a random enemy.", "STARTER_FLARE.flavor": "PLACEHOLDER", "STARTER_FLARE.title": "Starter <PERSON><PERSON><PERSON>", "STONE_CALENDAR.description": "At the end of turn [blue]{DamageTurn}[/blue], deal [blue]{Damage}[/blue] damage to all enemies.", "STONE_CALENDAR.flavor": "The passage of time is imperceptible in the Spire.", "STONE_CALENDAR.title": "Stone Calendar", "STONE_HUMIDIFIER.description": "Whenever you [gold]Rest[/gold] at a [gold]Rest Site[/gold], raise your Max HP by [blue]{MaxHp}[/blue].", "STONE_HUMIDIFIER.flavor": "\"Has a soothing smell (placeholder)\"", "STONE_HUMIDIFIER.title": "Stone Humidifier", "STRANGE_HOOF.description": "Whenever you add a card that gains [gold]Block[/gold] to your [gold]Deck[/gold], [gold]Enchant[/gold] it with [blue]{NimbleAmount}[/blue] [purple]Nimble[/purple].", "STRANGE_HOOF.flavor": "PLACEHOLDER: I shudder to think what this came from.", "STRANGE_HOOF.title": "<PERSON>", "STRAWBERRY.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "STRAWBERRY.flavor": "\"Delicious! Haven't seen any of these since the blight.\" \n- <PERSON><PERSON><PERSON><PERSON>", "STRAWBERRY.title": "<PERSON><PERSON>berry", "STRIKE_DUMMY.description": "Cards containing “Strike” deal [blue]{ExtraDamage}[/blue] additional damage.", "STRIKE_DUMMY.flavor": "It's beat up.", "STRIKE_DUMMY.title": "Strike Dummy", "STURDY_BRACE.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "STURDY_BRACE.eventDescription": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "STURDY_BRACE.flavor": "PLACEHOLDER", "STURDY_BRACE.title": "<PERSON><PERSON><PERSON>", "SWORD_OF_JADE.description": "Start each combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "SWORD_OF_JADE.flavor": "Power powerful blade from an ancient civilization. You can tell it has slain many foes.", "SWORD_OF_JADE.title": "Sword of Jade", "SWORD_OF_STONE.description": "Transforms into a powerful relic after defeating [blue]{Elites}[/blue] {Elites:plural:Elite|Elites}.", "SWORD_OF_STONE.flavor": "PLACEHOLDER: A powerful weapon from a bygone era. You can still sense immense power being held within.", "SWORD_OF_STONE.title": "Sword of Stone", "SYMBIOTIC_VIRUS.description": "At the start of each combat, [gold]Channel[/gold] [blue]{Dark}[/blue] [gold]Dark[/gold].", "SYMBIOTIC_VIRUS.flavor": "A little bit of bad can do a lot of good...", "SYMBIOTIC_VIRUS.title": "Symbiotic Virus", "TANXS_MIGHT.description": "Whenever you play an [gold]Attack[/gold], gain [blue]{Strength}[/blue] [gold]Strength[/gold] this turn.", "TANXS_MIGHT.flavor": "PLACEHOLDER: It's 3 Vajras tied together.", "TANXS_MIGHT.title": "<PERSON><PERSON>'s Might", "TANXS_WHISTLE.description": "Upon pickup, add [blue]1[/blue] [gold]Whistle[/gold] to your [gold]Deck[/gold].", "TANXS_WHISTLE.eventDescription": "Add [blue]1[/blue] [gold]Whistle[/gold] to your [gold]Deck[/gold].", "TANXS_WHISTLE.flavor": "PLACEHOLDER", "TANXS_WHISTLE.title": "<PERSON><PERSON>'s Whistle", "TEA_OF_SHAME.description": "At the start of the next combat, shuffle [blue]{Da<PERSON><PERSON>ount}[/blue] Dazed into your [gold]Draw pile[/gold].", "TEA_OF_SHAME.flavor": "PLACEHOLDER: shame", "TEA_OF_SHAME.title": "Tea of Shame", "TEZCATARAS_CANDLE.description": "Gain {Energy:energyIcons()} at the start of each turn. Extinguishes at the start of [gold]Act[/gold] [blue]3[/blue].", "TEZCATARAS_CANDLE.flavor": "PLACEHOLDER: It seems the flame may go out at any minute.", "TEZCATARAS_CANDLE.title": "Tezcatara's Candle", "THE_ABACUS.description": "Gain [blue]{Block}[/blue] [gold]Block[/gold] whenever you shuffle your [gold]Draw Pile[/gold].", "THE_ABACUS.flavor": "\"One...Two...Three...\"", "THE_ABACUS.title": "The Abacus", "THE_BOOT.description": "Whenever you would deal [blue]{DamageThreshold}[/blue] or less unblocked attack damage, increase it to [blue]{DamageMinimum}[/blue].", "THE_BOOT.flavor": "When wound up, the boot grows larger in size.", "THE_BOOT.title": "The Boot", "THE_COURIER.description": "The merchant no longer runs out of cards, relics, or potions and his prices are reduced by [blue]{Discount}%[/blue].", "THE_COURIER.flavor": "The <PERSON>'s personal pet!", "THE_COURIER.title": "The Courier", "TIDES_BINDING.description": "You cannot heal for [blue]{Combats}[/blue] Combats.", "TIDES_BINDING.flavor": "PLACEHOLDER", "TIDES_BINDING.title": "Tide's Binding", "TOOLBOX.description": "At the start of each combat, choose [blue]1[/blue] of [blue]{Cards}[/blue] random Colorless cards and add the chosen card into your [gold]Hand[/gold].", "TOOLBOX.flavor": "A tool for every job.", "TOOLBOX.title": "Toolbox", "TOXIC_EGG.description": "Whenever you add a [gold]Skill[/gold] into your [gold]Deck[/gold], [gold]Upgrade[/gold] it.", "TOXIC_EGG.flavor": "\"What a marvelous discovery! This appears to be the inert egg of some magical creature. Who or what created this?\" - <PERSON><PERSON><PERSON><PERSON>", "TOXIC_EGG.title": "Toxic Egg", "TRAVELERS_FLASK.additionalRestSiteHealText": "[green]Procure a random [gold]potion[/gold].[/green]", "TRAVELERS_FLASK.description": "Whenever you rest, procure a random potion.", "TRAVELERS_FLASK.flavor": "PLACEHOLDER: It appears to be bottomless. The liquid changes every time you drink.", "TRAVELERS_FLASK.title": "Traveler's Flask", "TUNGSTEN_ROD.description": "Whenever you would lose HP, lose [blue]{HpLossReduction}[/blue] less.", "TUNGSTEN_ROD.flavor": "It's very very heavy.", "TUNGSTEN_ROD.title": "<PERSON><PERSON><PERSON>", "TWISTED_FUNNEL.description": "At the start of combat, apply [blue]{Poison}[/blue] [gold]Poison[/gold] to all enemies.", "TWISTED_FUNNEL.flavor": "PLACEHOLDER", "TWISTED_FUNNEL.title": "Twisted Funnel", "UNCEASING_TOP.description": "Whenever you have no cards in [gold]Hand[/gold] during your turn, draw a card.", "UNCEASING_TOP.flavor": "The top continues to spin effortlessly as if you were in a dream.", "UNCEASING_TOP.title": "Unceasing Top", "UNDULATING_MASS.description": "Whenever you would receive a [gold]Curse[/gold], gain [blue]{MaxHp}[/blue] Max HP instead.", "UNDULATING_MASS.flavor": "PLACEHOLDER: Curses are part of a balanced breakfast.", "UNDULATING_MASS.title": "Undulating Mass", "USED_NOTEPAD.description": "At the start of [gold]Boss[/gold] combats, upgrade [blue]{Cards}[/blue] random {Cards:plural:card|cards} in your [gold]Draw Pile[/gold] for the rest of combat.", "USED_NOTEPAD.flavor": "PLACEHOLDER: It's full of obscure rants and strange formulas.", "USED_NOTEPAD.title": "Used Notepad", "VAJRA.description": "Start each combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "VAJRA.flavor": "An ornamental relic given to warriors displaying glory in battle.", "VAJRA.title": "<PERSON><PERSON><PERSON>", "VAMBRACE.description": "The first time you gain [gold]Block[/gold] from a card each combat, double the amount gained.", "VAMBRACE.flavor": "PLACEHOLDER", "VAMBRACE.title": "<PERSON><PERSON><PERSON><PERSON>", "VELVET_CHOKER.description": "Gain {Energy:energyIcons()} at the start of your turn. You cannot play more than [blue]{Cards}[/blue] {Cards:plural:card|cards} per turn.", "VELVET_CHOKER.flavor": "\"Immense power, but too limited.\" - <PERSON><PERSON><PERSON> the Great", "VELVET_CHOKER.title": "<PERSON>", "VENOM_DISCOVERY.description": "See [blue]{Cards}[/blue] [gold]Silent[/gold] cards. Choose any number of them to add to your [gold]Deck[/gold].", "VENOM_DISCOVERY.flavor": "PLACEHOLDER", "VENOM_DISCOVERY.selectionScreenPrompt": "Select Any Number of Cards to Add to Your Deck.", "VENOM_DISCOVERY.title": "Venom Discovery", "VIBRANT_HALO.description": "Upon pickup, [gold]Transform[/gold] {StarterCard.StringValue:cond:[gold]{StarterCard}[/gold] into [gold]{AncientCard}[/gold]|a starter card with an ancient version}.", "VIBRANT_HALO.eventDescription": "Replace {StarterCard.StringValue:cond:[gold]{StarterCard}[/gold] with [gold]{AncientCard}[/gold]|a starter card with an ancient version}.", "VIBRANT_HALO.flavor": "PLACEHOLDER", "VIBRANT_HALO.title": "<PERSON><PERSON><PERSON>", "VINE_BRACELET.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "VINE_BRACELET.flavor": "PLACEHOLDER: An ancient tradition, popular at sleepovers.", "VINE_BRACELET.title": "Vine Bracelet", "VITRUVIAN_MINION.description": "Cards containing \"Minion\" no longer [gold]Exhaust[/gold].", "VITRUVIAN_MINION.flavor": "PLACEHOLDER", "VITRUVIAN_MINION.title": "Vitruvian Minion", "WAR_EFFIGY.description": "At the start of [gold]Boss[/gold] and [gold]Elite[/gold] combats, gain {Energy:energyIcons()} and draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "WAR_EFFIGY.flavor": "PLACEHOLDER: A small statue of <PERSON><PERSON>.", "WAR_EFFIGY.title": "War Effigy", "WAR_PAINT.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random [gold]{Cards:plural:Skill|Skills}[/gold].", "WAR_PAINT.flavor": "In the past, Ironclads would create wards using enchanted war paint before charging into battle.", "WAR_PAINT.title": "War Paint", "WAX_CHOKER.description": "Upon pickup, obtain [blue]{Relics}[/blue] [gold]Wax {Relics:plural:Relic|Relics}[/gold]. Every [blue]{Combats}[/blue] {Combats:plural:combat|combats}, your left-most [gold]Wax Relic[/gold] will melt away.", "WAX_CHOKER.eventDescription": "Obtain [blue]{Relics}[/blue] [gold]Wax {Relics:plural:Relic|Relics}[/gold]. Every [blue]{Combats}[/blue] {Combats:plural:combat|combats}, your left-most [gold]Wax Relic[/gold] will melt away.", "WAX_CHOKER.flavor": "PLACEHOLDER: The hardened wax accessory lets all know who your patron is.", "WAX_CHOKER.title": "Wax Choker", "WAX_CHOKER.waxRelicPrefix": "Wax {Title}", "WHETSTONE.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random [gold]{Cards:plural:Attack|Attacks}[/gold].", "WHETSTONE.flavor": "\"Flesh never beats steel.\" - <PERSON><PERSON><PERSON> the Great", "WHETSTONE.title": "Whetstone", "WHITE_BEAST_STATUE.description": "Potions always appear in combat rewards.", "WHITE_BEAST_STATUE.flavor": "A small white statue of a creature you have never seen before.", "WHITE_BEAST_STATUE.title": "White Beast Statue", "WONGOS_BARGAIN_TICKET.description": "Receive a random [gold]Common Relic[/gold] after [blue]{RemainingCombats}[/blue] {RemainingCombats:plural:combat|combats}.", "WONGOS_BARGAIN_TICKET.flavor": "It says \"Come back soon\" on the back.", "WONGOS_BARGAIN_TICKET.title": "<PERSON><PERSON>'s <PERSON><PERSON>n Ticket", "WONGO_CUSTOMER_APPRECIATION_BADGE.description": "Does nothing.", "WONGO_CUSTOMER_APPRECIATION_BADGE.flavor": "It's cheaply made.", "WONGO_CUSTOMER_APPRECIATION_BADGE.title": "Wongo Customer Appreciation Badge", "ZLATIRS_CAPE.description": "At the start of each turn, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}. You may not draw cards during your turn.", "ZLATIRS_CAPE.flavor": "PLACEHOLDER: The thick cloth shimmers in the night and is surprisingly warm. Stylish and practical.", "ZLATIRS_CAPE.title": "Zlatir's Cape"}