using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.Json;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization.Formatters;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Modding;
using MegaCrit.Sts2.Core.Saves;
using SmartFormat;
using SmartFormat.Core.Formatting;
using SmartFormat.Extensions;
using FileAccess = Godot.FileAccess;

namespace MegaCrit.Sts2.Core.Localization;

public class LocManager
{
    // Singleton instance
    public static LocManager Instance { get; private set; } = default!;

    private Dictionary<string, LocTable> _tables = [];
    private static SmartFormatter _smartFormatter = default!;
    private static string LocalizationAssetDir => "res://localization";

    private readonly List<LocaleChangeCallback> _localeChangeCallbacks = [];
    private IClimbState _climbState = NullClimbState.Instance;

    private string _language;

    public string Language
    {
        get => _language;

        [MemberNotNull(nameof(CultureInfo), nameof(_language))]
        private set
        {
            _language = value;
            CultureInfo = CultureInfoFromThreeLetterCode(_language);
            TranslationServer.SetLocale(CultureInfo.Name);
        }
    }

    public static List<string> Languages { get; } =
    [
        "eng", // English
        // "ara", // Arabic
        // "ben", // Bengali
        // "cze", // Czech
        "zhs", // Chinese (Simplified)
        // "zht", // Chinese (Traditional)
        "deu", // German (Deutsche)
        // "dut", // Dutch
        // "esp", // Espanol (LatAm Spanish)
        // "fil", // Filipino (Standard Tagalog)
        // "fin", // Finnish
        "fra", // French
        // "gre", // Greek
        // "hin", // Hindi
        // "ind", // Indonesian
        // "ita", // Italian
        "jpn", // Japanese
        "kor", // Korean
        // "mal", // Malaysian Malay. NOTE: Speakers of Malay can understand Indonesian Malay
        // "nor", // Norwegian
        "pol", // Polish
        // "por", // Portuguese
        // "ptb", // Portuguese Brazilian
        "rus", // Russian
        // "spa", // Spanish (European/Castilian Spanish). NOTE: Speakers of Castilian can understand LatAm Espanol
        // "swe", // Swedish
        // "tha", // Thai
        // "tur", // Turkish
        // "ukr", // Ukrainian
        // "vie", // Vietnamese
    ];

    public CultureInfo CultureInfo { get; private set; }

    /// <summary>
    /// Initialize the singleton LocManager.
    /// This is favored over a static constructor so that we can precisely control the initialization time relative to
    /// other steps (mods must be initialized before this).
    /// </summary>
    public static void Initialize()
    {
        Instance = new LocManager();
    }

    public LocManager()
    {
        string? language = SaveManager.Instance.SettingsSave.Language;

        // Bandaid for ISO 639-1 to ISO 639-2.
        // TODO: Incorporate into save migrator
        if (language == "en")
        {
            language = "eng";
        }

        // If user has not set language, set the language to the default language on the system
        if (string.IsNullOrEmpty(language))
        {
            // For now, always initialize to eng. Otherwise, the player will get www language, which is not great.
            // The below commented code should work fine and is tested, so just uncomment it when we have translations
            language = "eng";

            /*
            // I'm skeptical if this will work in all instances. Might need to patch this later
            // The replacement from underscore to dash seems to get it to behave more correctly. zh_TW is parsed as
            // "Chinese (Sort Order=TW)", whereas zh-TW is correctly parsed as "Chinese (Taiwan)"
            CultureInfo cultureInfo = new ($"{OS.GetLocale().Replace('_', '-')}");
            language = ThreeLetterCodeFromCultureInfo(cultureInfo);
            SaveManager.Instance.SettingsSave.Language = language;

            Log.Info($"Initialized language for the first time from locale {OS.GetLocale()}. Language from system settings: {language} (CultureInfo: {cultureInfo})");
            */
        }

        SetLanguage(language);
        LoadLocFormatters();
    }

    /// <summary>
    /// Converts our three-letter code to a CultureInfo.
    /// Most of our language codes are ISO 639-2 language codes, but not all of them.
    /// </summary>
    private CultureInfo CultureInfoFromThreeLetterCode(string language)
    {
        CultureInfo? info;

        // First, try getting the culture info directly from the ISO 369-2 language code, if the code is one.
        info = CultureInfo.GetCultures(CultureTypes.NeutralCultures).FirstOrDefault(c => c.ThreeLetterISOLanguageName == _language);
        if (info != null) return info;

        // If this fails, then use our own mapping.
        if (language == "zhs")
        {
            // Simplified chinese
            return CultureInfo.GetCultureInfo("zh-hans");
        }
        else if (language == "zht")
        {
            // Traditional chinese
            return CultureInfo.GetCultureInfo("zh-hant");
        }
        else if (language == "ptb")
        {
            // Brazilian Portuguese
            return CultureInfo.GetCultureInfo("pt-br");
        }

        throw new InvalidOperationException($"Language code {language} could not be mapped to CultureInfo! Add a new manual mapping");
    }

    private string ThreeLetterCodeFromCultureInfo(CultureInfo cultureInfo)
    {
        if (Languages.Contains(cultureInfo.ThreeLetterISOLanguageName))
        {
            return cultureInfo.ThreeLetterISOLanguageName;
        }

        string cultureName = cultureInfo.Name.ToLower();
        cultureName = cultureName.Replace('-', '_');

        // This is an incomplete mapping and will probably grow quite a bit as we experience new platforms and receive bugs.
        // Do not hesitate to add more stuff here over time.
        if (cultureName.StartsWith("zh"))
        {
            if (cultureName.StartsWith("zh_hans") || cultureName.StartsWith("zh_cn"))
            {
                return "zhs";
            }

            if (cultureName.StartsWith("zh_hant") || cultureName.StartsWith("zh_tw"))
            {
                return "zht";
            }

            return "zhs";
        }
        else if (cultureName.StartsWith("pt"))
        {
            if (cultureName.StartsWith("pt_br"))
            {
                return "ptb";
            }

            return "por";
        }

        Log.Error($"CultureInfo {cultureInfo} could not be mapped to a three-letter language code! Defaulting to eng");
        return "eng";
    }

    /// <summary>
    /// This is where we explicitly load the localization formatters we need and only the ones we need.
    /// </summary>
    private void LoadLocFormatters()
    {
        _smartFormatter = new SmartFormatter();
        ListFormatter listFormatter = new();

        _smartFormatter.AddExtensions(
            listFormatter,
            // new PersistentVariablesSource(smartFormatter), // not used
            new DictionarySource(),
            new ValueTupleSource(),
            // new XmlSource(formatter), // not used
            new ReflectionSource(),
            new DefaultSource() // reproduces the string.Format behavior:
        );

        // Explicitly set all the localization formatters. Note: they are executed in order
        _smartFormatter.AddExtensions(
            listFormatter,
            new PluralLocalizationFormatter(),
            new ConditionalFormatter(),
            // new TimeFormatter(), // not used
            // new XElementFormatter(), // not used
            new ChooseFormatter(),
            new SubStringFormatter(),
            new IsMatchFormatter(),
            new DefaultFormatter(),
            new AbsoluteValueFormatter(),
            new EnergyIconsFormatter(_climbState),
            new StarIconsFormatter(),
            new HighlightDifferencesFormatter(),
            new HighlightDifferencesInverseFormatter(),
            new PercentMoreFormatter(),
            new PercentLessFormatter(),
            new ShowIfUpgradedFormatter()
        );

        Smart.Default = _smartFormatter;
    }

    /// <summary>
    /// Set the current climb state.
    /// Should only ever be called by <see cref="ClimbManager.Launch"/>.
    /// An unfortunate necessity to make energy icons render properly.
    /// </summary>
    public void SetClimbState(IClimbState climbState)
    {
        _climbState = climbState;
        _smartFormatter.GetFormatterExtension<EnergyIconsFormatter>()?.SetClimbState(climbState);
    }

    /// <summary>
    /// Clear the current climb state.
    /// Should only ever be called by <see cref="ClimbManager.CleanUp"/>.
    /// An unfortunate necessity to make energy icons render properly.
    /// </summary>
    public void ClearClimbState()
    {
        _climbState = NullClimbState.Instance;
        _smartFormatter.GetFormatterExtension<EnergyIconsFormatter>()?.SetClimbState(NullClimbState.Instance);
    }

    public string SmartFormat(LocString locString, Dictionary<string, object> variables)
    {
        string rawText = locString.GetRawText();

        try
        {
            // return ConvertToW(_smartFormatter.Format(rawText, variables));
            return _smartFormatter.Format(rawText, variables);
        }
        catch (FormattingException e)
        {
            throw new LocException($"message={e.Message}\ntable={locString.LocTable} key={locString.LocEntryKey} variables={ToString(variables)}");
        }
    }

    /// <summary>
    /// For localization debug
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private static string ConvertToW(string input)
    {
        char[] charArray = new char[input.Length];

        for (int i = 0; i < input.Length; i++)
        {
            char currentChar = input[i];

            if (char.IsUpper(currentChar))
            {
                charArray[i] = 'W';
            }
            else if (char.IsLower(currentChar) || char.IsDigit(currentChar) || char.IsPunctuation(currentChar))
            {
                charArray[i] = 'w';
            }
            else if (char.IsWhiteSpace(currentChar))
            {
                charArray[i] = currentChar; // Leave spaces untouched
            }
            else
            {
                charArray[i] = 'w'; // Default to 'w' for any other characters
            }
        }

        return new string(charArray);
    }

    private static string ToString(Dictionary<string, object> variables)
    {
        return "{" + string.Join(",", variables.Select(kp => $"{kp.Key}:{kp.Value}")) + "}";
    }

    /// <summary>
    /// Updates what language is being used for translation.
    /// Note that, since this is called temporarily sometimes, that it does not update the settings save file.
    /// </summary>
    /// <param name="language">The three-letter code of the language to load. These are based on ISO 639-2, but are not
    /// all actual 639-2 codes because they don't map exactly to what we need.</param>
    [MemberNotNull(nameof(CultureInfo), nameof(_language))]
    public void SetLanguage(string language)
    {
        _tables = LoadTablesFromPath(language);
        Language = language;

        // Run all callbacks for locale change.
        TriggerLocaleChange();
        // Suggest to the gc that it should cleanup while we load a new locale data
        GC.Collect();
    }

    private static Dictionary<string, LocTable> LoadTablesFromPath(string language)
    {
        Dictionary<string, LocTable> locTables = new();
        string localePath = $"{LocalizationAssetDir}/{language}";

        // TODO: This fallback is temporary for development and should likely be removed at some point
        if (DirAccess.Open(localePath) == null)
        {
            Log.Warn($"Dir path {localePath} for language {language} does not exist, falling back to www");
            localePath = $"{LocalizationAssetDir}/www";
        }

        Log.Info($"Loading locale path={localePath}");
        IEnumerable<string> files = ListLocalizationFiles(localePath);
        foreach (string path in files)
        {
            string name = Path.GetFileNameWithoutExtension(path);
            string baseTablePath = $"{localePath}/{path}";
            Dictionary<string, string> baseTable = LoadTable(baseTablePath);
            LocTable locTable = new(name, baseTable);

            foreach (string moddedTable in ModManager.GetModdedLocTables(language, path))
            {
                Log.Info($"Found loc table from mod: {language} {path}. Merging with base loc table");
                Dictionary<string, string> modTable = LoadTable(moddedTable);
                locTable.MergeWith(modTable);
            }

            locTables[name] = locTable;
        }

        return locTables;
    }

    // public static LocaleIdentifier[] GetAvailableLocales()
    // {
    // List<LocaleIdentifier> locales = new List<LocaleIdentifier>();
    //
    // foreach (string dir in Directory.GetDirectories(LocalizationAssetDir))
    // {
    //     string localeCode = Path.GetFileName(dir);
    //
    //     LocaleIdentifier identifier = Locale.CreateLocale(localeCode).Identifier;
    //     if (identifier.CultureInfo == null)
    //     {
    //         Debug.LogWarning($"The locale located here is not recognized: {dir}");
    //         continue;
    //     }
    //     locales.Add(identifier);
    // }
    //
    // return locales.ToArray();
    // }

    public LocTable GetTable(string name)
    {
        if (_tables.TryGetValue(name, out LocTable? locTable))
        {
            return locTable;
        }

        throw new LocException($"The loc table='{name}' does not exist!");
    }

    private static Dictionary<string, string> LoadTable(string path)
    {
        FileAccess file = FileAccess.Open(path, FileAccess.ModeFlags.Read);
        if (file == null) throw new LocException($"Cannot find language file: {path}");

        string jsonContent = file.GetAsText();

        try
        {
            Dictionary<string, string> data = JsonSerializer.Deserialize<Dictionary<string, string>>(jsonContent)!;
            return data;
        }
        catch (Exception e)
        {
            throw new LocException($"Failed to parse language file: {path}", e);
        }
    }

    private static IEnumerable<string> ListLocalizationFiles(string path)
    {
        DirAccess dir = DirAccess.Open(path);
        if (dir != null)
        {
            return dir.GetFiles().Where(s => s.EndsWith(".json"));
        }

        throw new LocException($"Path does not exist: {path}");
    }


    public delegate void LocaleChangeCallback();

    public void SubscribeToLocaleChange(LocaleChangeCallback callback)
    {
        _localeChangeCallbacks.Add(callback);
    }

    public void UnsubscribeToLocaleChange(LocaleChangeCallback callback)
    {
        _localeChangeCallbacks.Remove(callback);
    }

    private void TriggerLocaleChange()
    {
        foreach (LocaleChangeCallback cb in _localeChangeCallbacks)
        {
            cb.Invoke();
        }
    }
}
