using System;
using System.Text.Json.Serialization;
using Godot;
using MegaCrit.Sts2.Core.Modding;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Saves;

/// <summary>
/// This class serves as a schema for the Settings save file.
/// It's used for serializing and deserializing data in the options screen.
/// This file is NOT to be synced across devices/platforms.
/// </summary>
public class SettingsSave : ISaveSchema
{
    /// <summary>
    /// The schema version of this save.
    /// </summary>
    [JsonPropertyName("schema_version")]
    public int SchemaVersion { get; set; }

    [JsonPropertyName("fps_limit")] // NOTE: Remove for Consoles
    public int FpsLimit { get; set; } = 60;

    [JsonPropertyName("language")]
    public string? Language { get; set; }

    [JsonPropertyName("window_position")] // NOTE: Not used by Mobile/Consoles
    public Vector2I WindowPosition { get; set; } = new(-1, -1);

    [JsonPropertyName("window_size")] // NOTE: Not used by Mobile/Consoles
    public Vector2I WindowSize { get; set; } = new(1920, 1080);

    [JsonPropertyName("resize_windows")] // NOTE: Not used by Mobile/Consoles
    public bool ResizeWindows { get; set; } = true;

    [JsonPropertyName("vsync")] // NOTE: Not used by Mobile/Consoles
    public VSyncType VSync { get; set; } = VSyncType.Adaptive;

    [JsonPropertyName("msaa")] // NOTE: Not used by Mobile/Consoles
    public int Msaa { get; set; } = 2;

    [JsonPropertyName("volume_bgm")]
    public float VolumeBgm { get; set; } = 0.5f;

    [JsonPropertyName("volume_master")]
    public float VolumeMaster { get; set; } = 0.5f;

    [JsonPropertyName("volume_sfx")]
    public float VolumeSfx { get; set; } = 0.5f;

    [JsonPropertyName("volume_ambience")]
    public float VolumeAmbience { get; set; } = 0.5f;

    [JsonPropertyName("fullscreen")] // NOTE: Not used by Mobile/Consoles
    public bool Fullscreen { get; set; } = true;

    [JsonPropertyName("aspect_ratio")] // NOTE: Not used by Mobile/Consoles
    public AspectRatioSetting AspectRatioSetting { get; set; } = AspectRatioSetting.SixteenByNine;

    [JsonPropertyName("target_display")] // NOTE: Not used by Mobile/Consoles
    public int TargetDisplay { get; set; } = -1;

    [JsonPropertyName("fast_mode")]
    public FastModeType FastMode { get; set; } = FastModeType.Normal;

    // Why is this a float? It is a mystery to me and, indeed, everyone
    [JsonPropertyName("screenshake")]
    public float ScreenShakeOptionIndex { get; set; } = 1f;

    [JsonPropertyName("screenshake_mode")]
    [Obsolete("Use ScreenShakeOptionIndex instead")]
    public ScreenShakeMode Deprecated { get; set; } = ScreenShakeMode.Normal;

    [JsonPropertyName("show_run_timer")] // NOTE: Not used by Mobile/Consoles
    public bool ShowRunTimer { get; set; }

    [JsonPropertyName("show_card_indices")]
    public bool ShowCardIndices { get; set; }

    [JsonPropertyName("upload_data")] // NOTE: Not used by Mobile/Consoles
    public bool UploadData { get; set; } = true;

    [JsonPropertyName("mute_in_background")]
    public bool MuteInBackground { get; set; } = true;

    [JsonPropertyName("mod_settings")]
    public ModSettings? ModSettings { get; set; }
}
