using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using System.Text.Json;
using Godot;
using HarmonyLib;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Platform.Steam;
using MegaCrit.Sts2.Core.Saves;
using Steamworks;
using FileAccess = Godot.FileAccess;

namespace MegaCrit.Sts2.Core.Modding;

public static class ModManager
{
    private static List<Mod> _mods = [];
    private static List<Mod> _loadedMods = [];
    private static bool _initialized;

#if !DISABLESTEAMWORKS
    private static Callback<ItemInstalled_t>? _steamItemInstalledCallback;
#endif

    public static IReadOnlyList<Mod> AllMods => _mods;
    public static IReadOnlyList<Mod> LoadedMods => _loadedMods;

    /// <summary>
    /// Called when a new mod is detected. Use this to display a warning at runtime if the user adds new mods.
    /// </summary>
    public static event Action<Mod>? OnModDetected;

    /// <summary>
    /// Loads all mods from the appropriate directories. This includes the "mods" directory next to the executable, as
    /// well as Steam workshop files.
    /// This should be called as early as possible in the game's initialization process so that downstream classes
    /// (ModelDb, LocManager) pick up the changes that the mods apply.
    /// </summary>
    public static void Initialize()
    {
        AppDomain.CurrentDomain.AssemblyResolve += HandleAssemblyResolveFailure;

        // Load all mod files, including DLLs and PCKs
        string executablePath = OS.GetExecutablePath();
        string executableDir = Path.GetDirectoryName(executablePath)!;
        DirAccess? dirAccess = DirAccess.Open(Path.Combine(executableDir, "mods"));

        if (dirAccess != null)
        {
            LoadModsInDirRecursive(dirAccess, ModSource.ModsDirectory);
        }

#if !DISABLESTEAMWORKS
        if (SteamInitializer.Initialized)
        {
            InitializeSteamMods();
        }
#endif

        _loadedMods = _mods.Where(m => m.wasLoaded).ToList();

        if (_loadedMods.Count > 0)
        {
            Log.Info($" --- RUNNING MODDED! --- Loaded {_loadedMods.Count} mods ({_mods.Count} total)");
        }

        _initialized = true;
    }

    /// <summary>
    /// Recurses through the directory and loads all the mod PCKs and DLLs in it.
    /// We allow mods in directories to give modders a little slack in how they organize their files.
    /// </summary>
    private static void LoadModsInDirRecursive(DirAccess dirAccess, ModSource source)
    {
        foreach (string pckFilename in dirAccess.GetFiles())
        {
            if (!pckFilename.EndsWith(".pck")) continue;

            Log.Info($"Found mod pck file {dirAccess.GetCurrentDir()}/{pckFilename}");
            TryLoadModFromPck(pckFilename, dirAccess, source);
        }

        foreach (string directory in dirAccess.GetDirectories())
        {
            LoadModsInDirRecursive(DirAccess.Open(Path.Combine(dirAccess.GetCurrentDir(), directory)), source);
        }
    }

#if !DISABLESTEAMWORKS
    private static void InitializeSteamMods()
    {
        uint subscribedItemCount = SteamUGC.GetNumSubscribedItems();
        PublishedFileId_t[] workshopItems = new PublishedFileId_t[subscribedItemCount];
        subscribedItemCount = SteamUGC.GetSubscribedItems(workshopItems, subscribedItemCount);

        for (int i = 0; i < subscribedItemCount; i++)
        {
            PublishedFileId_t workshopItemId = workshopItems[i];
            TryLoadModFromSteam(workshopItemId);
        }

        // Listen for new steam workshop items installed during runtime
        _steamItemInstalledCallback = Callback<ItemInstalled_t>.Create(OnSteamWorkshopItemInstalled);
    }

    private static void TryLoadModFromSteam(PublishedFileId_t workshopItemId)
    {
        bool success = SteamUGC.GetItemInstallInfo(workshopItemId, out ulong sizeOnDisk, out string folder, 256, out uint timestamp);

        if (!success)
        {
            Log.Warn($"Could not get Steam Workshop item install info for item {workshopItemId.m_PublishedFileId}");
            return;
        }

        Log.Info($"Looking for mods to load from Steam Workshop mod {workshopItemId.m_PublishedFileId} in {folder} (size {sizeOnDisk}, last modified {timestamp})");
        DirAccess dirAccess = DirAccess.Open(folder);
        LoadModsInDirRecursive(dirAccess, ModSource.SteamWorkshop);
    }

    private static void OnSteamWorkshopItemInstalled(ItemInstalled_t ev)
    {
        if (ev.m_unAppID.m_AppId != SteamInitializer.steamAppId) return;
        Log.Info($"Detected new Steam Workshop item installation, id: {ev.m_nPublishedFileId.m_PublishedFileId}");
        TryLoadModFromSteam(ev.m_nPublishedFileId);
    }
#endif

    private static void TryLoadModFromPck(string pckFilename, DirAccess dirAccess, ModSource source)
    {
        Assembly? loadedAssembly = null;

        string pckName = Path.GetFileNameWithoutExtension(pckFilename);
        bool modIsDisabled = SaveManager.Instance.SettingsSave.ModSettings?.IsModDisabled(pckName, source) ?? false;
        bool modAlreadyLoaded = _mods.Any(m => m.manifest?.pckName == pckName);
        if (modIsDisabled || modAlreadyLoaded || _initialized)
        {
            if (_initialized)
            {
                Log.Info($"Skipping loading mod {pckFilename}, can't load mods at runtime");
            }
            else if (modIsDisabled)
            {
                Log.Info($"Skipping loading mod {pckFilename}, it is set to disabled in settings");
            }
            else if (modAlreadyLoaded)
            {
                Log.Warn($"Tried to load mod with PCK name {pckName}, but a mod is already loaded with that name!");
            }

            Mod mod = new()
            {
                pckName = pckName,
                modSource = source,
                manifest = null,
                wasLoaded = false
            };

            _mods.Add(mod);
            OnModDetected?.Invoke(mod);
            return;
        }

        try
        {
            if (!File.Exists(Path.Combine(dirAccess.GetCurrentDir(), pckFilename)))
            {
                throw new InvalidOperationException($"PCK not found at path {pckFilename}!");
            }

            // Load the associated DLL file first to ensure that any scripts are loaded before the PCK is.
            string dllFilename = $"{pckName}.dll";
            if (dirAccess.FileExists(dllFilename))
            {
                Log.Info($"Loading assembly DLL {dllFilename}");
                if (AssemblyLoadContext.GetLoadContext(Assembly.GetExecutingAssembly()) is { } loadContext)
                {
                    loadedAssembly = loadContext.LoadFromAssemblyPath(Path.Combine(dirAccess.GetCurrentDir(), dllFilename));
                }
            }

            // Load the PCK file.
            bool loadedPckSuccessfully = ProjectSettings.LoadResourcePack(Path.Combine(dirAccess.GetCurrentDir(), pckFilename));

            if (!loadedPckSuccessfully)
            {
                throw new InvalidOperationException($"Godot errored while loading PCK file {pckName}!");
            }

            // Load the PCK file's mod manifest.
            if (!ResourceLoader.Exists("res://mod_manifest.json"))
            {
                throw new InvalidOperationException($"{pckFilename} did not supply a mod manifest!");
            }

            using FileAccessStream manifestStream = new("res://mod_manifest.json", FileAccess.ModeFlags.Read);
            ModManifest? modManifest = JsonSerializer.Deserialize<ModManifest>(manifestStream, JsonSerializationUtility.serializerOptions);

            if (modManifest == null)
            {
                throw new InvalidOperationException("JSON deserialization returned null when trying to deserialize mod manifest!");
            }

            // Ensure the PCK mod manifest matches what we expect.
            // Note that there is a subtle gotcha here. We need the mod manifest to always be at a known location so that
            // we can read it without knowing any information about the mod. However, the mod manifest gets overwritten
            // every time we load a new mod because of the behavior of ProjectSettings.LoadResourcePack. That means if
            // this mod does not contain any manifest, we'll still see the old one, which is why we need to ensure the
            // PCK matches what we expect before proceeding.
            if (!string.Equals(modManifest.pckName, pckName, StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException($"PCK name in mod manifest {modManifest.pckName} does not match the pck {pckName} we're currently loading!");
            }

            // Run the initializer for the loaded assembly, or run harmony patches if no initializer is present
            if (loadedAssembly != null)
            {
                List<Type> initializers = loadedAssembly.GetTypes().Where(t => t.GetCustomAttribute<ModInitializerAttribute>() != null).ToList();

                if (initializers.Count > 0)
                {
                    foreach (Type initializer in initializers)
                    {
                        CallModInitializer(initializer);
                    }
                }
                else
                {
                    Log.Info($"No ModInitializerAttribute detected. Calling Harmony.PatchAll for {loadedAssembly}");
                    Harmony harmony = new($"{modManifest.author ?? "unknown"}.{pckName}");
                    harmony.PatchAll(loadedAssembly);
                }
            }

            Log.Info($"Finished mod initialization for '{modManifest.name}' ({modManifest.pckName}).");

            Mod mod = new()
            {
                pckName = pckName,
                modSource = source,
                manifest = modManifest,
                wasLoaded = true
            };

            _mods.Add(mod);
            OnModDetected?.Invoke(mod);
        }
        catch (Exception e)
        {
            // Mod loading can fail for all sorts of reasons, including mistakes by the mod developer.
            // If loading one mod fails, we don't want to interrupt loading other mods, and we really don't want to
            // interrupt game initialization, so log the error and continue
            Log.Error($"Error loading mod {pckFilename}: {e}");
        }
    }

    /// <summary>
    /// Calls the mod initializer on a type that has the ModInitializerAttribute on it.
    /// </summary>
    private static void CallModInitializer(Type initializerType)
    {
        ModInitializerAttribute attribute = initializerType.GetCustomAttribute<ModInitializerAttribute>()!;
        MethodInfo? methodInfo = initializerType.GetMethod(attribute.initializerMethod, BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);

        if (methodInfo == null)
        {
            // Check if it's declared non-static
            methodInfo = initializerType.GetMethod(attribute.initializerMethod, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);

            if (methodInfo != null)
            {
                Log.Error($"Tried to call mod initializer {initializerType.Name}.{attribute.initializerMethod} but it's not static! Declare it to be static");
            }
            else
            {
                Log.Error($"Found mod initializer class of type {initializerType}, but it does not contain the method {attribute.initializerMethod} declared in the ModInitializerAttribute!");
            }

            return;
        }

        // No exceptions from foreign code should leak into our code
        try
        {
            Log.Info($"Calling initializer method {initializerType.Name}.{attribute.initializerMethod}...");
            methodInfo.Invoke(null, null);
        }
        catch (Exception e)
        {
            Log.Error($"Exception thrown when calling mod initializer of type {initializerType}: {e}");
        }
    }

    /// <summary>
    /// Returns the filenames of all the loc tables available in loaded mods for the given language and filename.
    /// For example, if "eng" and "cards.json" are provided, this returns all mods that supply a cards.json in english.
    /// </summary>
    public static IEnumerable<string> GetModdedLocTables(string language, string file)
    {
        foreach (Mod mod in _mods)
        {
            if (!mod.wasLoaded) continue;

            string filename = $"res://{mod.manifest!.pckName}/localization/{language}/{file}";
            if (!ResourceLoader.Exists(filename)) continue;
            yield return filename;
        }
    }

    /// <summary>
    /// Resolves assemblies loaded by mods that may have a different version.
    /// When writing a mod DLL, the implementer may target a STS2 DLL that does not match the current version. When the
    /// dotnet runtime attempts to load the DLL, it tries to strictly match the version. Usually, we don't really need
    /// it to - a lot of our APIs don't change that often. Attaching this method to the AssemblyResolve event allows us
    /// to force dotnet to resolve the assembly to the correct one.
    /// </summary>
    private static Assembly HandleAssemblyResolveFailure(object? source, ResolveEventArgs ev)
    {
        if (ev.Name.StartsWith("sts2,"))
        {
            Log.Info($"Failed to resolve assembly '{ev.Name}' but it looks like the STS2 assembly. Resolving using {Assembly.GetExecutingAssembly()}");
            return Assembly.GetExecutingAssembly();
        }
        else if (ev.Name.StartsWith("0Harmony,"))
        {
            Log.Info($"Failed to resolve assembly '{ev.Name}' but it looks like the Harmony assembly. Resolving using {typeof(Harmony).Assembly}");
            return typeof(Harmony).Assembly;
        }

        // This is a different assembly that we don't know about. Return null to allow dotnet to continue.
        return null!;
    }

    public static void Dispose()
    {
#if !DISABLESTEAMWORKS
        _steamItemInstalledCallback?.Dispose();
#endif
    }
}
