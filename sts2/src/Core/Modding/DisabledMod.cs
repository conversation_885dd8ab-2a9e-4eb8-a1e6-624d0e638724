using System.Text.Json.Serialization;

namespace MegaCrit.Sts2.Core.Modding;

/// <summary>
/// Represents a disabled mod in the settings json
/// </summary>
public class DisabledMod
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = "";

    [JsonPropertyName("source")]
    public ModSource Source { get; set; }

    public DisabledMod()
    {
    }

    public DisabledMod(Mod mod)
    {
        Name = mod.pckName;
        Source = mod.modSource;
    }
}
