using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Modding;

public class ModSettings
{
    [JsonPropertyName("disabled_mods")]
    [JsonSerializeCondition(SerializationCondition.SaveIfNotCollectionEmptyOrNull)]
    public List<DisabledMod> DisabledMods { get; set; } = [];

    public bool IsModDisabled(Mod mod)
    {
        return IsModDisabled(mod.pckName, mod.modSource);
    }

    public bool IsModDisabled(string pckName, ModSource source)
    {
        return DisabledMods.Any(m => m.Name == pckName && m.Source == source);
    }
}
