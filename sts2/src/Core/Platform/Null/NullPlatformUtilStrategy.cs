using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Platform.Null;

public class NullPlatformUtilStrategy : IPlatformUtilStrategy
{
    public ulong LocalPlayerId { get; } = 1;

    public NullPlatformUtilStrategy()
    {
        if (CommandLineHelper.TryGetValue("clientId", out string? overrideClientIdStr) &&
            ulong.TryParse(overrideClientIdStr, out ulong overrideClientId))
        {
            LocalPlayerId = overrideClientId;
        }
    }

    public string GetPlayerName(ulong playerId)
    {
        return playerId switch
        {
            1 => "Test Host",
            1000 => "Test Client 1",
            2000 => "Test Client 2",
            3000 => "Test Client 3",
            _ => playerId.ToString()
        };
    }

    public ulong GetLocalPlayerId() => LocalPlayerId;
    public Task<IEnumerable<ulong>> GetFriendsWithOpenLobbies() => Task.FromResult((IEnumerable<ulong>)[]);
    public void OpenUrl(string url) => OS.ShellOpen(url);
}
