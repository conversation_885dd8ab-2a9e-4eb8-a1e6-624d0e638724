using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using Steamworks;

namespace MegaCrit.Sts2.Core.Platform.Steam;

public class SteamPlatformUtilStrategy : IPlatformUtilStrategy
{
    public string GetPlayerName(ulong playerId)
    {
        string playerName;

        if (playerId == SteamUser.GetSteamID().m_SteamID)
        {
            playerName = SteamFriends.GetPersonaName();
        }
        else
        {
            playerName = SteamFriends.GetFriendPersonaName(new CSteamID(playerId));
        }

        return string.IsNullOrEmpty(playerName) ? playerId.ToString() : playerName;
    }

    public ulong GetLocalPlayerId()
    {
        return SteamUser.GetSteamID().m_SteamID;
    }

    public Task<IEnumerable<ulong>> GetFriendsWithOpenLobbies()
    {
        List<ulong> friends = [];
        int friendCount = SteamFriends.GetFriendCount(EFriendFlags.k_EFriendFlagImmediate);

        for (int i = 0; i < friendCount; i++)
        {
            CSteamID friend = SteamFriends.GetFriendByIndex(i, EFriendFlags.k_EFriendFlagImmediate);

            if (SteamFriends.GetFriendGamePlayed(friend, out FriendGameInfo_t info) &&
                info.m_gameID.m_GameID == SteamInitializer.steamAppId &&
                info.m_steamIDLobby.IsValid())
            {
                friends.Add(friend.m_SteamID);
            }
        }

        return Task.FromResult((IEnumerable<ulong>)friends);
    }

    public void OpenUrl(string url)
    {
        if (SteamUtils.IsOverlayEnabled())
        {
            SteamFriends.ActivateGameOverlayToWebPage(url);
        }
        else
        {
            OS.ShellOpen(url);
        }
    }
}
