using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Modding;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Helpers;

public static class OneTimeInitialization
{
    private static bool _initialized;

    /// <summary>
    /// Should be called once at the start of the program execution.
    /// Needs to happen both in-game and in test runs, so it's in a util function.
    /// </summary>
    public static void Execute()
    {
        if (_initialized) return;

        _initialized = true;

        // Settings must be initialized early so that disabled mods are available to the mod loader
        // If we need to split the mod settings out we can do that, just more complicated
        SaveManager.Instance.InitSettingsData();

        // This must happen as early as possible so that modded content is loaded
        ModManager.Initialize();

        LocManager.Initialize();

        ModelDb.Init();
        ModelIdSerializationCache.Init();
        ModelDb.InitIds();

        // This initializes a singleton Regex instance in a static constructor that takes a long time to initialize,
        // and would otherwise initialize the first time we enter gameplay
        SmartFormat.Extensions.ConditionalFormatter dummy = new();
    }
}
