using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MysteryMachine : RelicModel
{
    private const string _unknownKey = "Unknown";

    public override RelicRarity Rarity => RelicRarity.Ancient;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new IntVar(_unknownKey, 6)
    ];

    public override Task AfterObtained()
    {
        AddUnknownsToMap((Owner.ClimbState as ClimbState)!, Owner.ClimbState.Map);
        return Task.CompletedTask;
    }

    public override ActMap ModifyGeneratedMapLate(ClimbState climbState, ActMap map, int actIndex)
    {
        return AddUnknownsToMap(climbState, map);
    }

    private ActMap AddUnknownsToMap(ClimbState climbState, ActMap map)
    {
        if (map is not StandardActMap standardMap) return map;
        if (standardMap.IsAffectedByMysteryMachine) return standardMap;

        Rng rng = new(climbState.Rng.Seed, "mystery_machine_map");

        for (int i = 0; i < DynamicVars[_unknownKey].IntValue; i++)
        {
            IEnumerable<MapPoint> validUnknownRooms = standardMap.GetAllMapPoints().Where(p =>
                p.PointType is MapPointType.Monster or MapPointType.Elite or MapPointType.Shop
                && standardMap.IsValidPointType(MapPointType.Unknown, p));

            MapPoint? mapPoint = rng.NextItem(validUnknownRooms);

            if (mapPoint != null)
            {
                mapPoint.PointType = MapPointType.Unknown;
            }
        }

        standardMap.IsAffectedByMysteryMachine = true;
        return standardMap;
    }
}
