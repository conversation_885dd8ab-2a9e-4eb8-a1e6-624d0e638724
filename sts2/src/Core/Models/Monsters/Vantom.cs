using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Vantom : MonsterModel
{
    private const string _vantomCustomTrackName = "vantom_progress";

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 173, 163);
    public override int MaxInitialHp => MinInitialHp;

    private int InkBlotDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 6);
    private int InkyLanceDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private const int _inkyLanceRepeat = 2;
    private int DismemberDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 26, 24);
    private const int _dismemberWounds = 2;
    private const int _prepareStrength = 2;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;
    public override bool ShouldDisappearFromDoom => false;

    private const string _chargeUpTrigger = "CHARGE_UP";
    private const string _buffTrigger = "BUFF";
    private const string _debuffTrigger = "DEBUFF";

    private const string _heavyAttackTrigger = "ATTACK_HEAVY";

    private const string _buffSfx = "event:/sfx/enemy/enemy_attacks/vantom/vantom_buff";
    private const string _dismemberSfx = "event:/sfx/enemy/enemy_attacks/vantom/vantom_dismember";
    private const string _extend1Sfx = "event:/sfx/enemy/enemy_attacks/vantom/vantom_extend_1";
    private const string _extend2Sfx = "event:/sfx/enemy/enemy_attacks/vantom/vantom_extend_2";
    private const string _extend3Sfx = "event:/sfx/enemy/enemy_attacks/vantom/vantom_extend_2";
    private const string _inkyLanceSfx = "event:/sfx/enemy/enemy_attacks/vantom/vantom_inky_lance";

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();

        int slipperyAmount = AscensionHelper.GetValueIfAscension(AscensionLevel.MightyBosses, 11, 9);
        await PowerCmd.Apply<Slippery>(Creature, slipperyAmount, Creature, null);
        Creature.Died += AfterDeath;
    }

    private void AfterDeath(Creature _)
    {
        Creature.Died -= AfterDeath;
        NClimbMusicController.Instance?.UpdateMusicParameter(_vantomCustomTrackName, 5);
    }

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        node.SpineController!.GetAnimationState().SetAnimation("_tracks/charge_up_1", false, 1);
        node.SpineController!.GetAnimationState().AddAnimation("_tracks/charged_1", 0, true, 1);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState inkBlotMove = new("INK_BLOT_MOVE", InkBlotMove, new SingleAttackIntent(InkBlotDamage));
        MoveState inkyLanceMove = new("INKY_LANCE_MOVE", InkyLanceMove, new MultiAttackIntent(InkyLanceDamage, _inkyLanceRepeat));
        MoveState dismemberMove = new("DISMEMBER_MOVE", DismemberMove, new SingleAttackIntent(DismemberDamage), new StatusIntent(_dismemberWounds));
        MoveState prepareMove = new("PREPARE_MOVE", PrepareMove, new BuffIntent());

        inkBlotMove.FollowUpState = inkyLanceMove;
        inkyLanceMove.FollowUpState = dismemberMove;
        dismemberMove.FollowUpState = prepareMove;
        prepareMove.FollowUpState = inkBlotMove;

        states.Add(inkBlotMove);
        states.Add(inkyLanceMove);
        states.Add(dismemberMove);
        states.Add(prepareMove);

        return new MonsterMoveStateMachine(states, inkBlotMove);
    }

    private async Task InkBlotMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(InkBlotDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.35f)
            .WithAttackerFx(sfx: _inkyLanceSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        if (TestMode.IsOff && Creature.IsAlive)
        {
            NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;

            await Cmd.CustomScaledWait(1.0f, 1.0f, 0.75f);
            NClimbMusicController.Instance?.UpdateMusicParameter(_vantomCustomTrackName, 1);
            SfxCmd.Play(_extend2Sfx);
            await CreatureCmd.TriggerAnim(Creature, _chargeUpTrigger, 0.15f);
            node.SpineController!.GetAnimationState().SetAnimation("_tracks/charge_up_2", false, 1);
            node.SpineController!.GetAnimationState().AddAnimation("_tracks/charged_2", 0, true, 1);
        }
    }

    private async Task InkyLanceMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(InkyLanceDamage, _inkyLanceRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_debuffTrigger, 0.4f)
            .WithAttackerFx(sfx: _inkyLanceSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        if (TestMode.IsOff && Creature.IsAlive)
        {
            NClimbMusicController.Instance?.UpdateMusicParameter(_vantomCustomTrackName, 2);
            await Cmd.CustomScaledWait(1.0f, 1.0f, 0.75f);
            SfxCmd.Play(_extend3Sfx);
            NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            node.SpineController!.GetAnimationState().SetAnimation("_tracks/charge_up_3", false, 1);
            node.SpineController!.GetAnimationState().AddAnimation("_tracks/charged_3", 0, true, 1);
            await CreatureCmd.TriggerAnim(Creature, _chargeUpTrigger, 0.15f);
        }
    }

    private async Task DismemberMove(IReadOnlyList<Creature> targets)
    {
        if (TestMode.IsOff && Creature.IsAlive)
        {
            NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            node.SpineController!.GetAnimationState().SetAnimation("_tracks/attack_heavy", false, 1);
            node.SpineController!.GetAnimationState().AddAnimation("_tracks/charged_0", 0, true, 1);
        }

        NClimbMusicController.Instance?.UpdateMusicParameter(_vantomCustomTrackName, 3);
        await CreatureCmd.TriggerAnim(Creature, _heavyAttackTrigger, 0.0f);

        // need an unscaled wait here (instead of the scale one in trigger anim) so the hit stop is timed correctly
        await Cmd.Wait(0.25f);
        VfxCmd.PlayOnSide(CombatSide.Player, VfxCmd.giantHorizontalSlashPath, Creature.CombatState!);

        NCombatRoom.Instance?.RadialBlur(VfxPosition.Left);
        SfxCmd.Play(_dismemberSfx);
        NGame.Instance?.ScreenShake(ShakeStrength.Strong, ShakeDuration.Normal, 180f + Rng.Chaotic.NextFloat(-10f, 10f));
        await CreatureCmd.Damage(targets, DismemberDamage, DamageProps.monsterMove, Creature, null);
        NGame.Instance!.DoHitStop(ShakeStrength.Weak, ShakeDuration.Short);

        await Cmd.Wait(0.5f); // some breathing room between the big attack and when the wounds spawn in
        await CardPileCmd.AddToCombatAndPreview<Wound>(targets, CardPileTarget.Discard, _dismemberWounds, false);
    }

    public async Task PrepareMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_buffSfx);
        await CreatureCmd.TriggerAnim(Creature, _buffTrigger, 0.6f);
        await PowerCmd.Apply<Strength>(Creature, _prepareStrength, Creature, null);

        if (TestMode.IsOff && Creature.IsAlive)
        {
            await Cmd.CustomScaledWait(1.0f, 1.0f, 0.75f);
            NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            SfxCmd.Play(_extend1Sfx);
            node.SpineController!.GetAnimationState().SetAnimation("_tracks/charge_up_1", false, 1);
            node.SpineController!.GetAnimationState().AddAnimation("_tracks/charged_1", 0, true, 1);
            await CreatureCmd.TriggerAnim(Creature, _chargeUpTrigger, 0.25f);
        }
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState buffAnim = new("buff");
        AnimState debuffAnim = new("debuff");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);
        AnimState chargeUpAnim = new("charge_up");
        AnimState attackHeavyAnim = new("attack_heavy");

        buffAnim.NextState = idleAnim;
        debuffAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;
        chargeUpAnim.NextState = idleAnim;
        attackHeavyAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);
        animator.AddAnyState(_chargeUpTrigger, chargeUpAnim);
        animator.AddAnyState(_heavyAttackTrigger, attackHeavyAnim);
        animator.AddAnyState(_buffTrigger, buffAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);
        animator.AddAnyState(_debuffTrigger, debuffAnim);

        return animator;
    }
}
