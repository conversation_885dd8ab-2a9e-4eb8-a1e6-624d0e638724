using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TrackerRubyRaider : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 22, 21);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 26, 25);

    private int HoundsDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 1, 1);
    private int HoundsRepeat => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = new();

        MoveState trackState = new("TRACK_MOVE", TrackMove, new DebuffIntent());
        MoveState houndsState = new("HOUNDS_MOVE", HoundsMove, new MultiAttackIntent(HoundsDamage, HoundsRepeat));
        trackState.FollowUpState = houndsState;
        houndsState.FollowUpState = houndsState;

        states.Add(trackState);
        states.Add(houndsState);

        return new MonsterMoveStateMachine(states, trackState);
    }

    private async Task TrackMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 0.8f);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.slashPath);
        await PowerCmd.Apply<Frail>(targets, 2, Creature, null);
    }

    private async Task HoundsMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(HoundsDamage, HoundsRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }
}
