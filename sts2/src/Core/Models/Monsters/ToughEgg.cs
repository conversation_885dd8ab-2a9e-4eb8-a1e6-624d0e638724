using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ToughEgg : MonsterModel
{
    private bool _hatched;

    public override LocString Title => _hatched ? L10NMonsterLookup("HATCHLING.name") : L10NMonsterLookup($"{Id.Entry}.name");
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 15, 14);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 20, 19);

    public int HatchlingMinHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 22, 21);
    public int HatchlingMaxHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 27, 26);

    private const string _hatchTrigger = "Hatch";

    private int NibbleDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private int MunchDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int ChompDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 12, 11);

    private const string _hatchSfx = "event:/sfx/enemy/enemy_attacks/tough_egg/tough_egg_hatch";

    public override string DeathSfx => _hatched ? "event:/sfx/enemy/enemy_attacks/tough_egg/hatchling_die" : "event:/sfx/enemy/enemy_attacks/tough_egg/tough_egg_die";

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    private static readonly string[] _eggOptions = ["egg1", "egg2"];

    private MonsterState? _afterHatchedState;

    public MonsterState? AfterHatchedState
    {
        get => _afterHatchedState;
        set
        {
            AssertMutable();
            _afterHatchedState = value;
        }
    }

    private bool _isHatched;

    public bool IsHatched
    {
        get => _isHatched;
        set
        {
            AssertMutable();
            _isHatched = value;
        }
    }

    private Vector2? _hatchPos;

    public Vector2? HatchPos
    {
        get => _hatchPos;
        set
        {
            AssertMutable();
            _hatchPos = value;
        }
    }

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkin compositeSkin = node.SpineController!.NewSkin("custom-skin");
        SpineSkeleton skeleton = node.SpineController!.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        compositeSkin.AddSkin(skeletonData.FindSkin(Rng.NextItem(_eggOptions)));

        skeleton.SetSkin(compositeSkin);
        skeleton.SetSlotsToSetupPose();
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();

        if (TestMode.IsOff && HatchPos != null)
        {
            NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            node.GlobalPosition = HatchPos.Value;
        }

        if (!IsHatched)
        {
            await PowerCmd.Apply<Hatch>(Creature, 1, Creature, null);
        }
        else
        {
            await Hatch();
            MoveStateMachine?.ForceCurrentState(AfterHatchedState!);
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState hatchState = new("HATCH_MOVE", HatchMove, new SummonIntent());
        RandomBranchState initialRandom = new("INITIAL_MOVE");
        MoveState nibbleState = new("NIBBLE_MOVE", NibbleMove, new SingleAttackIntent(NibbleDamage));
        MoveState munchState = new("MUNCH_MOVE", MunchMove, new SingleAttackIntent(MunchDamage));
        MoveState chompState = new("CHOMP_MOVE", ChompMove, new SingleAttackIntent(ChompDamage));

        initialRandom.AddBranch(nibbleState, MoveRepeatType.CanRepeatForever);
        initialRandom.AddBranch(munchState, MoveRepeatType.CanRepeatForever);
        initialRandom.AddBranch(chompState, MoveRepeatType.CanRepeatForever);

        hatchState.FollowUpState = initialRandom;
        nibbleState.FollowUpState = munchState;
        munchState.FollowUpState = chompState;
        chompState.FollowUpState = nibbleState;

        states.Add(hatchState);
        states.Add(initialRandom);
        states.Add(nibbleState);
        states.Add(munchState);
        states.Add(chompState);

        AfterHatchedState = initialRandom;

        return new MonsterMoveStateMachine(states, hatchState);
    }

    private async Task HatchMove(IReadOnlyList<Creature> targets)
    {
        IsHatched = true;
        await PowerCmd.Remove<Hatch>(Creature);
        _hatched = true;
        SfxCmd.Play(_hatchSfx);
        Creature.RemoveAllPowersExcept([Creature.GetPower<Minion>()!]);

        await Hatch();
    }

    private async Task Hatch()
    {
        await CreatureCmd.TriggerAnim(Creature, _hatchTrigger, 0.5f);

        decimal newMaxHp = Creature.ScaleHpForMultiplayer(
            ClimbRng.Niche.NextInt(HatchlingMinHp, HatchlingMaxHp),
            Creature.CombatState!.Players.Count,
            Creature.CombatState.Players[0].ClimbState.CurrentActIndex
        );
        await CreatureCmd.SetMaxHp(Creature, newMaxHp);
        await CreatureCmd.SetCurrentHp(Creature, Creature.MaxHp);
    }

    private async Task NibbleMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 0.2f);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.bitePath);
        await CreatureCmd.Damage(targets, NibbleDamage, DamageProps.monsterMove, Creature, null);
    }

    private async Task MunchMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 0.2f);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.bitePath);
        await CreatureCmd.Damage(targets, MunchDamage, DamageProps.monsterMove, Creature, null);
    }

    private async Task ChompMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 0.75f);
        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.bitePath);
        await CreatureCmd.Damage(targets, ChompDamage, DamageProps.monsterMove, Creature, null);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState dieAnim = new(AnimState.dieAnim);

        AnimState eggSpawnAnim = new("egg_spawn");
        AnimState eggIdleAnim = new("egg_idle_loop", true);
        AnimState eggHurtAnim = new("egg_hurt");
        AnimState eggDieAnim = new("egg_die");
        AnimState eggHatchAnim = new("egg_hatch");

        hurtAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        eggSpawnAnim.NextState = eggIdleAnim;
        eggHatchAnim.NextState = idleAnim;
        eggHurtAnim.NextState = eggIdleAnim;

        SpineAnimator animator = new(eggSpawnAnim, spineController);
        animator.AddAnyState(_hatchTrigger, eggHatchAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, eggHurtAnim, () => !IsHatched);
        animator.AddAnyState(SpineAnimator.deathTrigger, eggDieAnim, () => !IsHatched);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim, () => IsHatched);
        animator.AddAnyState(SpineAnimator.deathTrigger, dieAnim, () => IsHatched);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);

        return animator;
    }
}
