using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TwigSlimeM : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 28, 27);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 30, 29);

    private int ClumpDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 12, 11);
    private const int _stickyAmount = 1;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState clumpShotState = new("CLUMP_SHOT_MOVE", ClumpShotMove, new SingleAttackIntent(ClumpDamage));
        MoveState stickyShotState = new("STICKY_SHOT_MOVE", StickyShotMove, new StatusIntent(_stickyAmount));

        RandomBranchState randState = new("RAND");

        clumpShotState.FollowUpState = randState;
        stickyShotState.FollowUpState = randState;

        randState.AddBranch(clumpShotState, 2);
        randState.AddBranch(stickyShotState, MoveRepeatType.CannotRepeat);

        states.Add(clumpShotState);
        states.Add(stickyShotState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, stickyShotState);
    }

    private async Task ClumpShotMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ClumpDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slimeImpactVfxPath)
            .Execute();
    }

    private async Task StickyShotMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.75f);

        // ProjectileVfx slimeVfx = VfxFactory.CreateByPath(_slimeProjectileVfx).GetComponent<ProjectileVfx>();
        // await slimeVfx.PlayVfx(context.owner.transform, context.target.transform);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.slimeImpactVfxPath);
        await CardPileCmd.AddToCombatAndPreview<Slimed>(targets, CardPileTarget.Discard, _stickyAmount, false);
    }
}
