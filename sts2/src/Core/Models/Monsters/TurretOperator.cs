using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TurretOperator : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 44, 41);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 49, 49);
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Fur;

    private int FireDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);
    private const int _fireRepeat = 5;
    private const string _crankTrigger = "Crank";

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState unloadState1 = new("UNLOAD_MOVE_1", UnloadMove, new MultiAttackIntent(FireDamage, _fireRepeat));
        MoveState unloadState2 = new("UNLOAD_MOVE_2", UnloadMove, new MultiAttackIntent(FireDamage, _fireRepeat));
        MoveState reloadState = new("RELOAD_MOVE", ReloadMove, new BuffIntent());

        unloadState1.FollowUpState = unloadState2;
        unloadState2.FollowUpState = reloadState;
        reloadState.FollowUpState = unloadState1;

        states.Add(unloadState1);
        states.Add(unloadState2);
        states.Add(reloadState);

        return new MonsterMoveStateMachine(states, unloadState1);
    }

    private async Task ReloadMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, _crankTrigger, 0.4f);
        await PowerCmd.Apply<Strength>(Creature, 1, Creature, null);
    }

    private async Task UnloadMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(FireDamage, _fireRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.4f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState crankAnim = new("crank");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        crankAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(_crankTrigger, crankAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
