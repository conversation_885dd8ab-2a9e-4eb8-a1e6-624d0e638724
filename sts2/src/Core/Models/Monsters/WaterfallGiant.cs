using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class WaterfallGiant : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 260, 250);
    public override int MaxInitialHp => MinInitialHp;

    private int StompDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 13, 12);

    private int RamDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 11, 10);

    private int BasePressureGunDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 23, 20);

    public override bool ShouldDisappearFromDoom => !Creature.HasPower<SteamEruption>();
    private int PressureGunIncrease => 5;
    private int SiphonHeal => 15;

    private int _currentPressureGunDamage;

    private int CurrentPressureGunDamage
    {
        get => _currentPressureGunDamage;
        set
        {
            AssertMutable();
            _currentPressureGunDamage = value;
        }
    }

    private int _steamEruptionDamage;

    private int SteamEruptionDamage
    {
        get => _steamEruptionDamage;
        set
        {
            AssertMutable();
            _steamEruptionDamage = value;
        }
    }

    // This gets set in GenerateMoveStateMachine, which is effectively a constructor.
    private MoveState _aboutToBlowState = default!;

    private MoveState AboutToBlowState
    {
        get => _aboutToBlowState;
        set
        {
            AssertMutable();
            _aboutToBlowState = value;
        }
    }

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        CurrentPressureGunDamage = BasePressureGunDamage;
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState pressurizeState = new("PRESSURIZE_MOVE", PressurizeMove, new BuffIntent());
        MoveState stompState = new("STOMP_MOVE", StompMove, new SingleAttackIntent(StompDamage), new DebuffIntent());
        MoveState ramState = new("RAM_MOVE", RamMove, new SingleAttackIntent(RamDamage), new DefendIntent());
        MoveState siphonState = new("SIPHON_MOVE", SiphonMove, new HealIntent(), new BuffIntent());
        MoveState pressureGunState = new("PRESSURE_GUN_MOVE", PressureGunMove, new SingleAttackIntent(() => CurrentPressureGunDamage));

        AboutToBlowState = new MoveState("ABOUT_TO_BLOW_MOVE", AboutToBlowMove, new StunIntent()) { MustPerformOnceBeforeTransitioning = true };
        MoveState explodeState = new("EXPLODE_MOVE", ExplodeMove, new DeathBlowIntent(() => SteamEruptionDamage));
        RandomBranchState randState = new("RAND");

        pressurizeState.FollowUpState = stompState;
        stompState.FollowUpState = ramState;
        ramState.FollowUpState = siphonState;
        siphonState.FollowUpState = pressureGunState;
        pressureGunState.FollowUpState = pressurizeState;

        AboutToBlowState.FollowUpState = explodeState;
        explodeState.FollowUpState = explodeState;

        states.Add(randState);
        states.Add(pressurizeState);
        states.Add(stompState);
        states.Add(ramState);
        states.Add(siphonState);
        states.Add(pressureGunState);

        states.Add(explodeState);
        states.Add(AboutToBlowState);

        return new MonsterMoveStateMachine(states, pressurizeState);
    }

    private async Task PressurizeMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<SteamEruption>(Creature, 10, Creature, null);
    }

    private async Task StompMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(StompDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        await PowerCmd.Apply<Weak>(targets, 1, Creature, null);
    }

    private async Task RamMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(RamDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task SiphonMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.Heal(Creature, SiphonHeal * Creature.CombatState!.Players.Count);
        await PowerCmd.Apply<SteamEruption>(Creature, 5, Creature, null);
    }

    private async Task PressureGunMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(CurrentPressureGunDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        CurrentPressureGunDamage += PressureGunIncrease;
    }

    private async Task AboutToBlowMove(IReadOnlyList<Creature> targets)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);
        SteamEruptionDamage = Creature.GetPowerAmount<SteamEruption>();
        await PowerCmd.Remove<SteamEruption>(Creature);

        if (creatureNode != null)
        {
            creatureNode.Visuals.Modulate = Colors.Red;
        }
    }

    private async Task ExplodeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SteamEruptionDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .Execute();

        // TODO: Give the creature a sec before it dies. Should be able to get rid of this once
        // we have animation
        await Task.Delay(500);
        await CreatureCmd.Kill(Creature);
    }

    public async Task TriggerAboutToBlowState()
    {
        await CreatureCmd.SetMaxHp(Creature, Really.bigNumber);
        await CreatureCmd.SetCurrentHp(Creature, Really.bigNumber);
        Creature.ShowsInfiniteHp = true;

        // Do final move before dying
        SetMoveImmediate(AboutToBlowState);
    }
}
