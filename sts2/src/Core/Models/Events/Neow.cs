using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Neow : AncientEventModel
{
    private const string _cursedChoiceDoneDescriptionOverride = "NEOW.pages.DONE.CURSED.description";
    private const string _positiveChoiceDoneDescriptionOverride = "NEOW.pages.DONE.POSITIVE.description";

    // For testing specific neow options through the command line.
    public string? DebugOption
    {
        get => _debugOption;
        set
        {
            AssertMutable();
            _debugOption = value;
        }
    }

    private string? _debugOption;

    private const string _bountyHunterKey = "BOUNTY_HUNTER";
    private const string _bundleKey = "BUNDLE";
    private const string _chosenOneKey = "CHOSEN_ONE";
    private const string _empowerKey = "EMPOWER";
    private const string _transformationKey = "TRANSFORMATION";
    private const string _windfallKey = "WINDFALL";
    private const string _scavengerKey = "SCAVENGER";
    private const string _monochromeKey = "MONOCHROME";
    private const string _safetyKey = "SAFETY";
    private const string _leanKey = "LEAN";
    private const string _focusKey = "FOCUS";
    private const string _relicsKey = "RELICS";
    private const string _toughnessKey = "TOUGHNESS";
    private const string _valorKey = "VALOR";
    private const string _clericKey = "CLERIC";
    private const string _explorerKey = "EXPLORER";

    public override LocString InitialDescription
    {
        get
        {
            if (Owner?.ClimbState.Modifiers.Count <= 0)
            {
                return base.InitialDescription;
            }
            else
            {
                return L10NLookup($"{Id.Entry}.EVENT.description");
            }
        }
    }

    public override IEnumerable<EventOption> AllPossibleOptions => PositiveOptions.Concat(CurseOptions).Concat([ClericOption]);

    private IEnumerable<EventOption> PositiveOptions =>
    [
        RelicOption<BountyBox>(_bountyHunterKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<ChaoticPlasma>(_transformationKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<LeadPaperweight>(_monochromeKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<NeowsNote>(_windfallKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<RustyChest>(_scavengerKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<Scissors>(_leanKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<ShinyMonocle>(_chosenOneKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<SilverCrucible>(_empowerKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<StoneHumidifier>(_safetyKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<SturdyBrace>(_toughnessKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride),
        RelicOption<MysteryMachine>(_explorerKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride)

    ];

    private EventOption ClericOption => RelicOption<ClericsHeadpiece>(_clericKey, _initialPageKey, _positiveChoiceDoneDescriptionOverride);

    private IEnumerable<EventOption> CurseOptions =>
    [
        RelicOption<AncientBoosterPack>(_bundleKey, _initialPageKey, _cursedChoiceDoneDescriptionOverride),
        RelicOption<BellOfTwilight>(_valorKey, _initialPageKey, _cursedChoiceDoneDescriptionOverride),
        RelicOption<GiftBox>(_relicsKey, _initialPageKey, _cursedChoiceDoneDescriptionOverride),
        RelicOption<SpiraledLens>(_focusKey, _initialPageKey, _cursedChoiceDoneDescriptionOverride),

    ];

    public static IEnumerable<string> DebugEventOptionsTitles =>
    [
        //positive keys
        _bountyHunterKey,
        _transformationKey,
        _monochromeKey,
        _windfallKey,
        _scavengerKey,
        _leanKey,
        _chosenOneKey,
        _empowerKey,
        _safetyKey,
        _toughnessKey,

        //cursed keys
        _bundleKey,
        _valorKey,
        _relicsKey,
        _focusKey,

        //multiplayer
        _clericKey,
        _explorerKey,
    ];

    private List<EventOption>? _modifierOptions;

    private List<EventOption> ModifierOptions
    {
        get
        {
            AssertMutable();
            _modifierOptions ??= [];
            return _modifierOptions;
        }
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        // If there are no modifiers, generate the normal Neow benefits
        if (Owner!.ClimbState.Modifiers.Count <= 0)
        {
            List<EventOption> validPositives = PositiveOptions.ToList();

            if (Owner.ClimbState.Players.Count > 1)
            {
                validPositives.Add(ClericOption);
            }

            List<EventOption> options = validPositives.ToList().UnstableShuffle(Rng).Take(2).ToList();

            options.Add(Rng.NextItem(CurseOptions)!);
            if (DebugOption != null)
            {
                options.RemoveAt(0);
                options.Insert(0, AllPossibleOptions.First(c => c.TextKey.Contains(DebugOption)));
            }

            return options;
        }
        // If this is a custom/daily, Neow gives you no benefits and instead offers modifiers
        else
        {
            foreach (ModifierModel modifier in Owner!.ClimbState.Modifiers)
            {
                Func<Task>? neowOption = modifier.GenerateNeowOption(this);

                if (neowOption == null) continue;

                int optionIndex = ModifierOptions.Count;

                ModifierOptions.Add(new EventOption(
                    () => OnModifierOptionSelected(neowOption, optionIndex),
                    modifier.NeowOptionTitle,
                    modifier.NeowOptionDescription,
                    modifier.Id.Entry,
                    modifier.HoverTips.ToArray()));
            }

            if (ModifierOptions.Count > 0)
            {
                return [ModifierOptions[0]];
            }
            else
            {
                return [];
            }
        }
    }

    private async Task OnModifierOptionSelected(Func<Task> modifierFunc, int index)
    {
        await modifierFunc.Invoke();

        if (index + 1 >= ModifierOptions.Count)
        {
            // DO NOT call Done here - that updates the ancient entry
            SetEventFinished(L10NLookup($"{Id.Entry}.pages.DONE.description"));
        }
        else
        {
            SetEventState(InitialDescription, [ModifierOptions[index + 1]]);
        }
    }
}
