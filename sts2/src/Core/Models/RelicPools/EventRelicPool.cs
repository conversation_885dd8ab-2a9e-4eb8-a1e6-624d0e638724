using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.RelicPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class EventRelicPool : RelicPoolModel
{
    public override string EnergyColorName => ColorlessCardPool.energyColorName;

    protected override RelicModel[] GenerateRelics() =>
    [
        ModelDb.Relic<AncientBoosterPack>(),
        ModelDb.Relic<ApexInstinct>(),
        ModelDb.Relic<Astrolabe>(),
        ModelDb.Relic<BarnacledPipe>(),
        ModelDb.Relic<BedlamBeacon>(),
        ModelDb.Relic<BellOfTwilight>(),
        ModelDb.Relic<BigMushroom>(),
        ModelDb.Relic<BlackBlood>(),
        ModelDb.Relic<BlackStar>(),
        ModelDb.Relic<BloodSoakedRose>(),
        ModelDb.Relic<BoneTea>(),
        ModelDb.Relic<BountyBox>(),
        ModelDb.Relic<BrokenOrbs>(),
        ModelDb.Relic<BucketOfCoals>(),
        ModelDb.Relic<BuriedSeed>(),
        ModelDb.Relic<Byrdpip>(),
        ModelDb.Relic<CallingBell>(),
        ModelDb.Relic<ChaoticPlasma>(),
        ModelDb.Relic<ChoicesParadox>(),
        ModelDb.Relic<ChosenCheese>(),
        ModelDb.Relic<ClericsHeadpiece>(),
        ModelDb.Relic<CloudyClaw>(),
        ModelDb.Relic<CozyChair>(),
        ModelDb.Relic<CrimsonPendant>(),
        ModelDb.Relic<DarkstonePeriapt>(),
        ModelDb.Relic<DarleejingTea>(),
        ModelDb.Relic<DaughterOfTheWind>(),
        ModelDb.Relic<DemonDiscovery>(),
        ModelDb.Relic<DreamCatcher>(),
        ModelDb.Relic<DustBunny>(),
        ModelDb.Relic<DustyTome>(),
        ModelDb.Relic<Ectoplasm>(),
        ModelDb.Relic<ElectrifiedShard>(),
        ModelDb.Relic<EmberTea>(),
        ModelDb.Relic<EmptyCage>(),
        ModelDb.Relic<EndlessAppetite>(),
        ModelDb.Relic<Fable>(),
        ModelDb.Relic<FragrantMushroom>(),
        ModelDb.Relic<GalacticDust>(),
        ModelDb.Relic<GearDiscovery>(),
        ModelDb.Relic<GiftBox>(),
        ModelDb.Relic<GlassEye>(),
        ModelDb.Relic<GloopyEye>(),
        ModelDb.Relic<GlowingOrb>(),
        ModelDb.Relic<GoldenCompass>(),
        ModelDb.Relic<HandDrill>(),
        ModelDb.Relic<HistoryCourse>(),
        ModelDb.Relic<HungeringBelly>(),
        ModelDb.Relic<InfusedCore>(),
        ModelDb.Relic<Kaleidoscope>(),
        ModelDb.Relic<LastingCandy>(),
        ModelDb.Relic<LeadPaperweight>(),
        ModelDb.Relic<LichDiscovery>(),
        ModelDb.Relic<MagicPot>(),
        ModelDb.Relic<MawBank>(),
        ModelDb.Relic<MrStruggles>(),
        ModelDb.Relic<MysteryMachine>(),
        ModelDb.Relic<NeowsNote>(),
        ModelDb.Relic<NobleDiscovery>(),
        ModelDb.Relic<OddlyHeavyStone>(),
        ModelDb.Relic<OracleBone>(),
        ModelDb.Relic<PaelsFlesh>(),
        ModelDb.Relic<PaelsTears>(),
        ModelDb.Relic<PaelsWing>(),
        ModelDb.Relic<PandorasBox>(),
        ModelDb.Relic<PerpetualCoin>(),
        ModelDb.Relic<PhilosophersStone>(),
        ModelDb.Relic<PhylacteryUnbound>(),
        ModelDb.Relic<PitchBlackOil>(),
        ModelDb.Relic<PrismaticShard>(),
        ModelDb.Relic<PulsingCore>(),
        ModelDb.Relic<RazorTooth>(),
        ModelDb.Relic<RecursiveCore>(),
        ModelDb.Relic<Replicator>(),
        ModelDb.Relic<RetractableClaws>(),
        ModelDb.Relic<RingOfTheDrake>(),
        ModelDb.Relic<RoyalPoison>(),
        ModelDb.Relic<RubyEarrings>(),
        ModelDb.Relic<RunicPyramid>(),
        ModelDb.Relic<RustyChest>(),
        ModelDb.Relic<Scissors>(),
        ModelDb.Relic<SealOfGold>(),
        ModelDb.Relic<SeekingTentacle>(),
        ModelDb.Relic<SeveredGill>(),
        ModelDb.Relic<ShinyMonocle>(),
        ModelDb.Relic<SilverCrucible>(),
        ModelDb.Relic<SmolderingWick>(),
        ModelDb.Relic<SneckoEye>(),
        ModelDb.Relic<SoulBuddy>(),
        ModelDb.Relic<Sozu>(),
        ModelDb.Relic<SpecialSleeve>(),
        ModelDb.Relic<SpiraledLens>(),
        ModelDb.Relic<StoneHumidifier>(),
        ModelDb.Relic<SturdyBrace>(),
        ModelDb.Relic<SwordOfJade>(),
        ModelDb.Relic<SwordOfStone>(),
        ModelDb.Relic<TanxsMight>(),
        ModelDb.Relic<TanxsWhistle>(),
        ModelDb.Relic<TeaOfShame>(),
        ModelDb.Relic<TezcatarasCandle>(),
        ModelDb.Relic<TheBoot>(),
        ModelDb.Relic<TidesBinding>(),
        ModelDb.Relic<UndulatingMass>(),
        ModelDb.Relic<VelvetChoker>(),
        ModelDb.Relic<VenomDiscovery>(),
        ModelDb.Relic<VibrantHalo>(),
        ModelDb.Relic<WarEffigy>(),
        ModelDb.Relic<WaxChoker>(),
        ModelDb.Relic<WongoCustomerAppreciationBadge>(),
        ModelDb.Relic<WongosBargainTicket>(),
        ModelDb.Relic<ZlatirsCape>()
    ];
}
