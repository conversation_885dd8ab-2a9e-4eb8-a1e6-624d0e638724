using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Leaderboard;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Modding;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Debug;
using MegaCrit.Sts2.Core.Nodes.Debug.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Reaction;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Nodes.Screens.InspectScreens;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Platform.Steam;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Settings;
using Environment = System.Environment;

namespace MegaCrit.Sts2.Core.Nodes;

public partial class NGame : Control
{
    /// <summary>
    /// Signal used to force other Scenes to refresh their UI when the
    /// application Window is updated via updated Settings or ctrl-F or something.
    /// </summary>
    [Signal]
    public delegate void WindowChangeEventHandler();

    // The typical resolution we are working with in the Godot Editor.
    // Used to calculate position ratios for UI elements to be resolution agnostic.
    public static readonly Vector2 devResolution = new(1920f, 1080f);

    public static NGame? Instance { get; private set; }
    private NScreenShake _screenShake = default!;
    private static int? _mainThreadId; // Useful for debugging
    private static Window _window = default!;

#if !DISABLESTEAMWORKS
    private SteamJoinCallbackHandler? _joinCallbackHandler;
#endif

    private WorldEnvironment WorldEnvironment { get; set; } = default!;
    private NHitStop HitStop { get; set; } = default!;
    public NSceneContainer RootSceneContainer { get; private set; } = default!;
    public Node? HoverTipsContainer { get; private set; }
    public NInspectRelicScreen InspectRelicScreen { get; private set; } = default!;
    public NInspectCardScreen InspectCardScreen { get; private set; } = default!;

    public NMainMenu? MainMenu => RootSceneContainer.CurrentScene as NMainMenu;
    public NClimb? CurrentClimbNode => RootSceneContainer.CurrentScene as NClimb;
    public NTransition Transition { get; private set; } = default!;
    public NAudioManager AudioManager { get; private set; } = default!;
    public NRemoteMouseCursorContainer RemoteCursorContainer { get; private set; } = default!;
    public NControllerManager ControllerManager { get; private set; } = default!;
    public NReactionWheel ReactionWheel { get; private set; } = default!;
    public NReactionContainer ReactionContainer { get; private set; } = default!;
    public NCursorManager CursorManager { get; private set; } = default!;
    public NDebugAudioManager DebugAudio { get; private set; } = default!;
    public string? DebugSeedOverride { get; set; }
    public bool StartOnMainMenu { get; set; } = true;
    public static bool IsTrailerMode { get; private set; }
    public static bool IsDebugHidingHoverTips { get; private set; }
    public static bool IsDebugHidingProceedButton { get; private set; }
    public event Action? DebugToggleProceedButton;

    public override void _EnterTree()
    {
        if (Instance != null)
        {
            Log.Error("NGame already exists.");
            this.QueueFreeSafely();
            return;
        }

        Instance = this;

#if !DISABLESTEAMWORKS
        bool forceSteam = OS.GetCmdlineArgs().Contains("-force-steam");
        SteamInitializer.Initialize(this, forceSteam);
        _joinCallbackHandler = new SteamJoinCallbackHandler();
#endif

        OneTimeInitialization.Execute();

        // Grab references to other singleton-esque objects
        // this is because other classes may try to access these in their _Ready
        // functions before NGame._Ready has been called.
        RootSceneContainer = GetNode<NSceneContainer>("%RootSceneContainer");
        HoverTipsContainer = GetNode<Node>("%HoverTipsContainer");
        DebugAudio = GetNode<NDebugAudioManager>("%DebugAudioManager");
        AudioManager = GetNode<NAudioManager>("%AudioManager");
        RemoteCursorContainer = GetNode<NRemoteMouseCursorContainer>("%RemoteCursorContainer");
        ControllerManager = GetNode<NControllerManager>("%ControllerManager");
        CursorManager = GetNode<NCursorManager>("%CursorManager");
        ReactionWheel = GetNode<NReactionWheel>("%ReactionWheel");
        ReactionContainer = GetNode<NReactionContainer>("%ReactionContainer");
        WorldEnvironment = GetNode<WorldEnvironment>("%WorldEnvironment");
        InspectRelicScreen = GetNode<NInspectRelicScreen>("%InspectRelicScreen");
        InspectCardScreen = GetNode<NInspectCardScreen>("%InspectCardScreen");

        ModManager.OnModDetected += OnNewModDetected;
    }

    public override void _Ready()
    {
        LeaderboardManager.Initialize();

        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));

        _screenShake = GetNode<NScreenShake>("ScreenShake");
        HitStop = GetNode<NHitStop>("HitStop");
        _mainThreadId = Environment.CurrentManagedThreadId; // Save the main thread id

        SaveManager.Instance.InitProgressData();
        Callable.From(InitializeGraphicsPreferences).CallDeferred();

        AudioManager.SetMasterVol(SaveManager.Instance.SettingsSave.VolumeMaster);
        AudioManager.SetSfxVol(SaveManager.Instance.SettingsSave.VolumeSfx);
        AudioManager.SetAmbienceVol(SaveManager.Instance.SettingsSave.VolumeAmbience);
        AudioManager.SetBgmVol(SaveManager.Instance.SettingsSave.VolumeBgm);

        DebugAudio.SetMasterAudioVolume(SaveManager.Instance.SettingsSave.VolumeMaster);
        DebugAudio.SetSfxAudioVolume(SaveManager.Instance.SettingsSave.VolumeSfx);

        SetScreenShakeMultiplier(SaveManager.Instance.SettingsSave.ScreenShakeOptionIndex);

        OsDebugInfo.LogSystemInfo();

        Transition = GetNode<NTransition>("%GameTransitionRect");

        // WorldEnvironment is kind of expensive to have around all the time doing nothing. Remove it from the tree to
        // stop it from affecting rendering, but hang on to it and re-add when necessary
        this.RemoveChildSafely(WorldEnvironment);

        if (Environment.GetCommandLineArgs().Contains("-multiplayertest"))
        {
            NMultiplayerTest multiplayer = SceneHelper.Instantiate<NMultiplayerTest>("debug/multiplayer_test");
            RootSceneContainer.SetCurrentScene(multiplayer);
            TaskHelper.RunSafely(Transition!.FadeIn());
        }
        else if (Environment.GetCommandLineArgs().Contains("-bootstrap"))
        {
            NSceneBootstrapper bootstrapper = SceneHelper.Instantiate<NSceneBootstrapper>("debug/scene_bootstrapper");
            this.AddChildSafely(bootstrapper);
        }
        else if (StartOnMainMenu)
        {
            TaskHelper.RunSafely(LaunchMainMenu());
        }
    }

    /// <summary>
    /// Called when we set our Aspect Ratio to Auto mode OR when the size of the window is changed while Aspect Ratio is Auto.
    /// </summary>
    private void OnWindowChange()
    {
        EmitSignal(SignalName.WindowChange, SaveManager.Instance.SettingsSave.AspectRatioSetting == AspectRatioSetting.Auto);
    }

    public static bool IsMainThread()
    {
        // We null check because in some other `_Ready` methods, the main thread id is not set yet.
        // In such cases we should set it, since `_Ready` is always called on the main thread.
        if (_mainThreadId == null)
        {
            _mainThreadId = Environment.CurrentManagedThreadId;
            return true;
        }

        return _mainThreadId == Environment.CurrentManagedThreadId;
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        ModManager.Dispose();

#if !DISABLESTEAMWORKS
        _joinCallbackHandler?.Dispose();
        SteamInitializer.Uninitialize();
#endif
    }

    /// <summary>
    /// If the game is a release build.
    /// </summary>
    public static bool IsReleaseGame()
    {
#if DEBUG
        return false; // Disable for faster development iteration, but less smooth gameplay
#else
        return true;
#endif
    }

    /// <summary>
    /// Called when the game starts to apply the player's graphics settings.
    /// </summary>
    private void InitializeGraphicsPreferences()
    {
        ApplyDisplaySettings();
        ApplySyncSetting();
        Engine.MaxFps = SaveManager.Instance.SettingsSave.FpsLimit;
    }

    /// <summary>
    /// Used to update graphics settings when we change the resolution or display mode from the options screen.
    /// </summary>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public void ApplyDisplaySettings()
    {
        bool shouldEmitWindowChange = false;

        SettingsSave settingsSave = SaveManager.Instance.SettingsSave;

        // First time setup for TargetDisplay
        if (settingsSave.TargetDisplay == -1)
        {
            Log.Info("First time setup for display settings...");
            settingsSave.TargetDisplay = DisplayServer.GetPrimaryScreen();
        }

        Log.Info($"Applying display settings..." +
            $"\n  FULLSCREEN: {settingsSave.Fullscreen}" +
            $"\n  ASPECT_RATIO: ({settingsSave.AspectRatioSetting})" +
            $"\n  TARGET_DISPLAY: ({settingsSave.TargetDisplay})" +
            $"\n  WINDOW_SIZE: {settingsSave.WindowSize}" +
            $"\n  POSITION: {settingsSave.WindowPosition}");

        if (settingsSave.AspectRatioSetting != AspectRatioSetting.Auto)
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.Keep;
        }

        switch (settingsSave.AspectRatioSetting)
        {
            case AspectRatioSetting.Auto:
                // Auto is... complicated. This is handled per screen.
                // They must hook into Window changes by Connecting to NGame's WindowChangeEventHandler
                shouldEmitWindowChange = true;
                break;
            case AspectRatioSetting.FourByThree:
                _window.ContentScaleSize = new Vector2I(1680, 1260);
                break;
            case AspectRatioSetting.SixteenByTen:
                _window.ContentScaleSize = new Vector2I(1920, 1200);
                break;
            case AspectRatioSetting.SixteenByNine:
                _window.ContentScaleSize = new Vector2I(1920, 1080);
                break;
            case AspectRatioSetting.TwentyOneByNine:
                _window.ContentScaleSize = new Vector2I(2580, 1080);
                break;
            case AspectRatioSetting.None:
            default:
                throw new ArgumentOutOfRangeException($"Invalid Aspect Ratio: {settingsSave.AspectRatioSetting}");
        }

        // Apply Fullscreen/Window settings
        if (settingsSave.Fullscreen)
        {
            DisplayServer.WindowSetMode(DisplayServer.WindowMode.Fullscreen);

            // We want to fullscreen to the correct monitor but if the monitor no longer exists (unplugged?), we fallback
            Log.Info($"[Display] Setting FULLSCREEN on Display: {settingsSave.TargetDisplay + 1} of {DisplayServer.GetScreenCount()}");
            if (settingsSave.TargetDisplay + 1 > DisplayServer.GetScreenCount())
            {
                Log.Warn($"[Display] FAILED: Display {settingsSave.TargetDisplay} is missing. Fallback to primary.");
                DisplayServer.WindowSetCurrentScreen(DisplayServer.GetPrimaryScreen());
            }
            else
            {
                DisplayServer.WindowSetCurrentScreen(settingsSave.TargetDisplay);
            }
        }
        else
        {
            Log.Info($"[Display] Attempting WINDOWED mode on Display {settingsSave.TargetDisplay + 1} of {DisplayServer.GetScreenCount()} at position {settingsSave.WindowPosition}");
            DisplayServer.WindowSetMode(DisplayServer.WindowMode.Windowed);

            int windowPosCommandLineIndex = Environment.GetCommandLineArgs().IndexOf("-wpos");
            if (windowPosCommandLineIndex >= 0)
            {
                Log.Info("[Display] -wpos called. Applying special logic.");
                Vector2I windowPos = new(
                    int.Parse(Environment.GetCommandLineArgs()[windowPosCommandLineIndex + 1]),
                    int.Parse(Environment.GetCommandLineArgs()[windowPosCommandLineIndex + 2]));

                Vector2I startPos = DisplayServer.ScreenGetPosition(DisplayServer.WindowGetCurrentScreen());

                Log.Info($"Applying window position from command line arg: {windowPos} ({string.Join(",", Environment.GetCommandLineArgs())} {startPos})");
                DisplayServer.WindowSetPosition(startPos + windowPos);
            }
            else if (settingsSave.WindowPosition != new Vector2I(-1, -1))
            {
                // Disallow the top left corner of the game window from being offscreen from the target display.
                Vector2I windowPos = settingsSave.WindowPosition;
                Vector2I targetDisplaySize = DisplayServer.ScreenGetSize(settingsSave.TargetDisplay);

                if (windowPos.X < 0 || windowPos.Y < 0 || windowPos.X > targetDisplaySize.X || windowPos.Y > targetDisplaySize.Y)
                {
                    Log.Warn("[Display] WARN: Game Window was offscreen. Resetting to top left corner.");
                    windowPos = new Vector2I(8, 48);
                }

                // If the Display is missing, fallback to render on the primary Display
                if (settingsSave.TargetDisplay + 1 > DisplayServer.GetScreenCount())
                {
                    Log.Info($"[Display] FAILED: Display {settingsSave.TargetDisplay} is missing. Fallback to primary.");
                    DisplayServer.WindowSetSize(DisplayServer.ScreenGetSize(DisplayServer.GetPrimaryScreen()) - new Vector2I(8, 48));
                    DisplayServer.WindowSetPosition(DisplayServer.ScreenGetPosition(DisplayServer.GetPrimaryScreen()) + new Vector2I(8, 48));
                }
                else
                {
                    Vector2I displayPosition = DisplayServer.ScreenGetPosition(settingsSave.TargetDisplay);

                    // Disallow the game window from being larger than the target display's size
                    Vector2I windowSize = settingsSave.WindowSize;
                    if (windowSize.X > targetDisplaySize.X)
                    {
                        windowSize.X = targetDisplaySize.X;
                    }

                    if (windowSize.Y > targetDisplaySize.Y)
                    {
                        windowSize.Y = targetDisplaySize.Y;
                    }

                    Log.Info($"[Display] SUCCESS: {windowSize} Windowed mode in Display {settingsSave.TargetDisplay}: Position {displayPosition + windowPos}");
                    DisplayServer.WindowSetSize(windowSize);
                    DisplayServer.WindowSetPosition(displayPosition + windowPos);
                }
            }
        }

        // Explicitly check if we changed the Aspect Ratio to auto because...
        // the Window may not resize so we don't recalculate the UI nodes when this occurs.
        if (shouldEmitWindowChange)
        {
            EmitSignal(SignalName.WindowChange, settingsSave.AspectRatioSetting == AspectRatioSetting.Auto);
        }


        FixFullscreenBorderIssue(settingsSave);
    }

    /// <summary>
    /// If we are switching to fullscreen, currently there's a bug where there's a border around the window which
    /// stops mouse input. Hack around it for now, but in Godot 4.5 we should be able to just go to Fullscreen
    /// https://github.com/godotengine/godot/issues/63500
    /// https://github.com/godotengine/godot/pull/88329
    /// </summary>
    /// <param name="settingsSave"></param>
    private void FixFullscreenBorderIssue(SettingsSave settingsSave)
    {
        if (!settingsSave.Fullscreen) return;

        DisplayServer.WindowSetMode(DisplayServer.WindowMode.Windowed);
        DisplayServer.WindowSetSize(DisplayServer.ScreenGetSize(settingsSave.TargetDisplay) + new Vector2I(2, 2));
        DisplayServer.WindowSetPosition(DisplayServer.ScreenGetPosition(settingsSave.TargetDisplay) - new Vector2I(1, 1));
    }

    /// <summary>
    /// Used to update VSync settings when changed from the options screen.
    /// </summary>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static void ApplySyncSetting()
    {
        // WARNING: This code is fragile but isn't changed often.
        switch (SaveManager.Instance.SettingsSave.VSync)
        {
            case VSyncType.Off: // Off
                Log.Info("VSync: Off");
                DisplayServer.WindowSetVsyncMode(DisplayServer.VSyncMode.Disabled);
                break;
            case VSyncType.On: // On
                Log.Info("VSync: On");
                DisplayServer.WindowSetVsyncMode(DisplayServer.VSyncMode.Enabled);
                break;
            case VSyncType.Adaptive: // Adaptive
                Log.Info("VSync: Adaptive");
                DisplayServer.WindowSetVsyncMode(DisplayServer.VSyncMode.Adaptive);
                break;
            case VSyncType.None:
            case VSyncType.Mailbox:
            default:
                Log.Error($"Invalid VSync type: {SaveManager.Instance.SettingsSave.VSync}");
                break;
        }
    }

    /// <summary>
    /// Reset the game instance. Only meant to be called from tests.
    /// </summary>
    public static void Reset()
    {
        Instance?.QueueFreeSafely();
        Instance = null;
    }

    /// <summary>
    /// Listens for game close request
    /// </summary>
    public override void _Notification(int what)
    {
        if (what == NotificationWMCloseRequest)
        {
            Quit();
        }
    }

    public void Quit()
    {
        // NOTE: This is Desktop-only logic. Be sure to hide via flags for console/mobile
        if (!SaveManager.Instance.SettingsSave.Fullscreen)
        {
            SaveManager.Instance.SettingsSave.WindowSize = DisplayServer.WindowGetSize();
            SaveManager.Instance.SettingsSave.TargetDisplay = DisplayServer.WindowGetCurrentScreen();
            SaveManager.Instance.SettingsSave.WindowPosition = DisplayServer.WindowGetPosition() - DisplayServer.ScreenGetPosition(SaveManager.Instance.SettingsSave.TargetDisplay);
        }

        // NOTE: Will this collide with Steam Autocloud? If issues arise, only allow saving while client is open
        // That's not a problem if we don't use Autocloud, we're going to use the Steam sdk so we won't need autocloud.
        SaveManager.Instance.SaveSettings();

        // TODO: Send a quit notification so different parts of the game can do quit logic before the game quits.
        // https://docs.godotengine.org/en/stable/tutorials/inputs/handling_quit_requests.html#sending-your-own-quit-notification
        GetTree().Quit();
    }

    /// <summary>
    /// Called when the Main Menu is launched for the first time
    /// </summary>
    private async Task LaunchMainMenu()
    {
        // Since we're launching from the start, we preload the main menu assets first so the screen loads faster,
        // then we load the common assets used for the whole game quietly in the background.
        await PreloadManager.LoadMainMenuAssets();
        await PreloadManager.LoadCommonAndMainMenuAssets(); // load in the background
        LoadMainMenu();

#if !DISABLESTEAMWORKS
        // This should only happen once when the game starts up, after the main menu finishes loading
        _joinCallbackHandler?.CheckForCommandLineJoin();
#endif
    }

    public async Task ReturnToMainMenuAfterClimb()
    {
        await ReturnToMainMenu();
    }

    public async Task ReturnToMainMenu()
    {
        await Transition.FadeOut();
        await PreloadManager.LoadCommonAndMainMenuAssets();
        ClimbManager.Instance.CleanUp();
        LoadMainMenu();
    }

    public void ReloadMainMenu()
    {
        if (MainMenu == null) throw new InvalidOperationException("Tried to reload main menu when not already on the main menu!");
        LoadMainMenu();
    }

    private void LoadMainMenu()
    {
        NMainMenu mainMenu = NMainMenu.Create();
        RootSceneContainer.SetCurrentScene(mainMenu);
    }

    /// <summary>
    /// Start a new single player climb on both the front and back ends.
    /// </summary>
    /// <param name="character">The character that the player is playing as.</param>
    /// <param name="shouldSave">Whether a save file should be created for the climb.</param>
    /// <param name="acts">The canonical acts that should be in the climb.</param>
    /// <param name="modifiers">The modifiers that are applied to the climb.</param>
    /// <param name="seed">The seed that the climb's RNG should use.</param>
    /// <param name="ascensionLevel">The ascension level that the climb should be played at.</param>
    /// <param name="dailyTime">Time used to determine the daily climb info. Null if not a daily climb.</param>
    public async Task<ClimbState> StartNewSingleplayerClimb(CharacterModel character, bool shouldSave, IReadOnlyList<ActModel> acts, IReadOnlyList<ModifierModel> modifiers, string seed, int ascensionLevel = 0, DateTimeOffset? dailyTime = null)
    {
        ClimbState climbState = ClimbState.CreateForNewClimb(
            [Player.CreateForNewClimb(character, NetSingleplayerGameService.defaultNetId)],
            acts.Select(a => a.ToMutable()).ToList(),
            modifiers,
            ascensionLevel,
            seed
        );

        ClimbManager.Instance.SetUpNewSinglePlayer(climbState, shouldSave, dailyTime);
        await StartClimb(climbState);

        return climbState;
    }

    /// <summary>
    /// Start a new multiplayer climb on both the front and back ends.
    /// </summary>
    /// <param name="lobby">The lobby containing the multiplayer info.</param>
    /// <param name="shouldSave">Whether a save file should be created for the climb.</param>
    /// <param name="acts">The canonical acts that should be in the climb.</param>
    /// <param name="modifiers">The modifiers that are applied to the climb.</param>
    /// <param name="seed">The seed that the climb's RNG should use.</param>
    /// <param name="ascensionLevel">The ascension level that the climb should be played at.</param>
    /// <param name="dailyTime">Time used to determine the daily climb info. Null if not a daily climb.</param>
    public async Task<ClimbState> StartNewMultiplayerClimb(StartClimbLobby lobby, bool shouldSave, IReadOnlyList<ActModel> acts, IReadOnlyList<ModifierModel> modifiers, string seed, int ascensionLevel, DateTimeOffset? dailyTime = null)
    {
        ClimbState climbState = ClimbState.CreateForNewClimb(
            lobby.Players.Select(p => Player.CreateForNewClimb(p.character, p.id)).ToList(),
            acts.Select(a => a.ToMutable()).ToList(),
            modifiers,
            ascensionLevel,
            seed
        );

        ClimbManager.Instance.SetUpNewMultiPlayer(climbState, lobby, shouldSave, dailyTime);
        await StartClimb(climbState);

        return climbState;
    }

    public async Task LoadClimb(ClimbState climbState, SerializableRoom? preFinishedRoom)
    {
        await PreloadManager.LoadClimbAssets(climbState.Players.Select(p => p.Character));
        await PreloadManager.LoadActAssets(climbState.Act);

        ClimbManager.Instance.Launch();

        RootSceneContainer.SetCurrentScene(NClimb.Create(climbState));

        await ClimbManager.Instance.GenerateMap();
        await ClimbManager.Instance.LoadIntoLatestMapCoord(AbstractRoom.FromSerializable(preFinishedRoom, climbState));

        // This needs to happen after GenerateMap because it clears the drawings (for switching between acts)
        if (ClimbManager.Instance.MapDrawingsToLoad != null)
        {
            NClimb.Instance!.GlobalUi.MapScreen.Drawings.LoadDrawings(ClimbManager.Instance.MapDrawingsToLoad);
            ClimbManager.Instance.MapDrawingsToLoad = null;
        }
    }

    private async Task StartClimb(ClimbState climbState)
    {
        await PreloadManager.LoadClimbAssets(climbState.Players.Select(p => p.Character));
        await ClimbManager.Instance.FinalizeStartingRelics();

        ClimbManager.Instance.Launch();
        RootSceneContainer.SetCurrentScene(NClimb.Create(climbState));
        await ClimbManager.Instance.EnterAct(0, false);
    }

    /// <summary>
    /// Used to scale the entire game's speed and down. Very different from Fast mode and is used for Trailer work or
    /// other game footage or bug capturing work.
    /// </summary>
    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent.IsActionReleased(DebugHotkey.speedUp))
        {
            DebugModifyTimescale(0.1);
        }
        else if (inputEvent.IsActionReleased(DebugHotkey.speedDown))
        {
            DebugModifyTimescale(-0.1);
        }
        else if (inputEvent.IsActionReleased(DebugHotkey.hideProceedButton))
        {
            IsDebugHidingProceedButton = !IsDebugHidingProceedButton;
            Instance!.AddChildSafely(NFullscreenTextVfx.Create(IsDebugHidingProceedButton ? "Hide Proceed Button" : "Show Proceed Button")!);
            DebugToggleProceedButton?.Invoke();
        }
        else if (inputEvent.IsActionReleased(DebugHotkey.hideHoverTips))
        {
            IsDebugHidingHoverTips = !IsDebugHidingHoverTips;
            Instance!.AddChildSafely(NFullscreenTextVfx.Create(IsDebugHidingHoverTips ? "Hide HoverTips" : "Show HoverTips")!);
        }
    }

    private void DebugModifyTimescale(double offset)
    {
        double newTimeScale = Math.Round(Engine.TimeScale + offset, 1);
        newTimeScale = Math.Clamp(newTimeScale, 0.1, 4.0);
        Engine.TimeScale = newTimeScale;
        this.AddChildSafely(NFullscreenTextVfx.Create($"TimeScale:{Engine.TimeScale}")!);
    }

    public WorldEnvironment ActivateWorldEnvironment()
    {
        this.AddChildSafely(WorldEnvironment);
        return WorldEnvironment;
    }

    public void DeactivateWorldEnvironment()
    {
        this.RemoveChildSafely(WorldEnvironment);
    }

    #region ScreenShake

    public void SetScreenShakeTarget(Control target)
    {
        _screenShake.SetTarget(target);
    }

    public void ClearScreenShakeTarget()
    {
        _screenShake.ClearTarget();
    }

    public void ScreenShake(ShakeStrength strength, ShakeDuration duration, float degAngle = -1f)
    {
        // Randomize angle if none is specified
        if (degAngle < 0f)
        {
            degAngle = Rng.Chaotic.NextFloat(360f);
        }

        // Log.Info($"{strength} screen shake for {duration} duration at {degAngle} degrees");
        _screenShake.Shake(strength, duration, degAngle);
    }

    public void ScreenRumble(ShakeStrength strength, ShakeDuration duration, RumbleStyle style)
    {
        _screenShake.Rumble(strength, duration, style);
    }

    public void ScreenShakeTrauma(ShakeStrength strength)
    {
        _screenShake.AddTrauma(strength);
    }

    public void DoHitStop(ShakeStrength strength, ShakeDuration duration)
    {
        HitStop.DoHitStop(strength, duration);
    }

    #endregion

    public static void ToggleTrailerMode()
    {
        IsTrailerMode = !IsTrailerMode;
    }

    public void SetScreenShakeMultiplier(float multiplier)
    {
        Log.Info($"Screenshake multiplier set to {multiplier}");
        _screenShake.SetMultiplier(multiplier);
    }

    /// <summary>
    /// Called when we detect that a mod is installed during runtime. Displays a warning telling the user that the mod
    /// won't be loaded until after a restart.
    /// </summary>
    private void OnNewModDetected(Mod mod)
    {
        // Ensure there isn't already a popup being shown (can happen if player installs multiple mods at once)
        if (!NModalContainer.Instance!.GetChildren().OfType<NErrorPopup>().Any())
        {
            NErrorPopup popup = NErrorPopup.Create(
                new LocString("main_menu_ui", "MOD_NOT_LOADED_POPUP.title"),
                new LocString("main_menu_ui", "MOD_NOT_LOADED_POPUP.description"),
                false)!;

            NModalContainer.Instance.AddChildSafely(popup);
        }
    }
}
