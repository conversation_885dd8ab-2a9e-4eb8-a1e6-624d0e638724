using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.FeedbackScreen;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Nodes.Screens.ModdingScreen;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

// TODO: I'm going to have the settings screen keep its unique backstop for now just
// so we dont have to do something special for the main menu. Revisit when we want to
// refactor the main menu screens stuff
public partial class NSettingsScreen : NSubmenu
{
    private NSettingsTabManager _settingsTabManager = default!;
    private NOpenFeedbackScreenButton _feedbackScreenButton = default!;
    private NSendFeedbackScreen _feedbackScreen = default!;
    private NOpenModdingScreenButton _moddingScreenButton = default!;
    private NModdingScreen _moddingScreen = default!;
    private NSettingsToast _toast = default!;

    [Signal]
    public delegate void SettingsClosedEventHandler();

    [Signal]
    public delegate void SettingsOpenedEventHandler();

    public static readonly Vector2 settingTipsOffset = new(1012f, -60f);

    private Control? _lastFocusedControl;

    public override void _Ready()
    {
        ConnectSignals();
        _settingsTabManager = GetNode<NSettingsTabManager>("%SettingsTabManager");
        _feedbackScreenButton = GetNode<NOpenFeedbackScreenButton>("%FeedbackButton");
        _moddingScreenButton = GetNode<NOpenModdingScreenButton>("%ModdingButton");
        _feedbackScreen = GetNode<NSendFeedbackScreen>("%FeedbackScreen");
        _moddingScreen = GetNode<NModdingScreen>("%ModdingScreen");
        _toast = GetNode<NSettingsToast>("%Toast");

        LocalizeLabels();
        ProcessMode = Visible ? ProcessModeEnum.Inherit : ProcessModeEnum.Disabled;

        _moddingScreenButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenModdingScreen));
        _feedbackScreenButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenFeedbackScreen));

        if (ClimbManager.Instance.IsInProgress)
        {
            // Cannot switch language during a run
            GetNode<Node>("ScrollContainer/Mask/GeneralSettings/VBoxContainer/Language").GetNode<MegaRichTextLabel>("MarginContainer/Label").Modulate = StsColors.gray;
            GetNode<NLanguageSelectorButton>("%LanguageSelector").Disable();

            // Cannot open modding settings during a run
            _moddingScreenButton.Disable();
        }
    }

    public void ShowToast(LocString locString)
    {
        _toast.Show(locString);
    }

    private void LocalizeLabels()
    {
        // General Settings
        Node general = GetNode<Node>("ScrollContainer/Mask/GeneralSettings/VBoxContainer");
        LocHelper(general.GetNode<Node>("Language"), new LocString("settings_ui", "LANGUAGE"));
        LocHelper(general.GetNode<Node>("CombatSpeed"), new LocString("settings_ui", "COMBAT_SPEED"));
        LocHelper(general.GetNode<Node>("Screenshake"), new LocString("settings_ui", "SCREENSHAKE"));
        LocHelper(general.GetNode<Node>("CommonTooltips"), new LocString("settings_ui", "COMMON_TOOLTIPS"));
        LocHelper(general.GetNode<Node>("ShowHandCardCount"), new LocString("settings_ui", "SHOW_HAND_CARD_COUNT_HEADER"));
        LocHelper(general.GetNode<Node>("UploadGameplayData"), new LocString("settings_ui", "UPLOAD_GAMEPLAY_DATA"));
        LocHelper(general.GetNode<Node>("ResetTutorials"), new LocString("settings_ui", "TUTORIAL_RESET"));

        // Graphics Settings
        Node graphics = GetNode<Node>("ScrollContainer/Mask/GraphicsSettings/VBoxContainer");
        LocHelper(graphics.GetNode<Node>("Fullscreen"), new LocString("settings_ui", "FULLSCREEN"));
        LocHelper(graphics.GetNode<Node>("VSync"), new LocString("settings_ui", "VSYNC"));
        LocHelper(graphics.GetNode<Node>("WindowedResolution"), new LocString("settings_ui", "RESOLUTION"));
        LocHelper(graphics.GetNode<Node>("AspectRatio"), new LocString("settings_ui", "ASPECT_RATIO"));
        LocHelper(graphics.GetNode<Node>("MaxFps"), new LocString("settings_ui", "FPS_CAP"));
        LocHelper(graphics.GetNode<Node>("Reset"), new LocString("settings_ui", "RESET_DEFAULT"));

        // Sound Settings
        Node sound = GetNode<Node>("ScrollContainer/Mask/SoundSettings/VBoxContainer");
        LocHelper(sound.GetNode<Node>("MasterVolume"), new LocString("settings_ui", "MASTER_VOLUME"));
        LocHelper(sound.GetNode<Node>("BgmVolume"), new LocString("settings_ui", "MUSIC_VOLUME"));
        LocHelper(sound.GetNode<Node>("SfxVolume"), new LocString("settings_ui", "SFX_VOLUME"));
        LocHelper(sound.GetNode<Node>("AmbienceVolume"), new LocString("settings_ui", "AMBIENCE_VOLUME"));
        LocHelper(sound.GetNode<Node>("MuteIfBackground"), new LocString("settings_ui", "BACKGROUND_MUTE"));

        // TODO: Input Settings
    }

    /// <summary>
    /// Helper class so the localize labels function doesn't look too busy.
    /// </summary>
    private static void LocHelper(Node settingsLineNode, LocString locString)
    {
        settingsLineNode.GetNode<MegaRichTextLabel>("MarginContainer/Label").Text = locString.GetFormattedText();
    }

    private void OpenModdingScreen(NButton _)
    {
        _stack.Push(_moddingScreen);
    }

    private void OpenFeedbackScreen(NButton _)
    {
        TaskHelper.RunSafely(OpenFeedbackScreen());
    }

    public async Task OpenFeedbackScreen()
    {
        Log.Info("Opening feedback screen");

        // When the feedback screen is opened, we want to grab a screenshot first, but without the settings menu visible.
        // Hide ourselves, wait a couple frames, grab a screenshot, and then come back and push the feedback screen
        Visible = false;
        NGame.Instance!.MainMenu?.DisableBackstopInstantly();
        NCapstoneContainer.Instance?.DisableBackstopInstantly();
        NClimb.Instance?.GlobalUi.RelicInventory.ShowImmediately();
        NClimb.Instance?.GlobalUi.MultiplayerPlayerContainer.ShowImmediately();

        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        Image screenshot = GetViewport().GetTexture().GetImage();

        NClimb.Instance?.GlobalUi.RelicInventory.HideImmediately();
        NClimb.Instance?.GlobalUi.MultiplayerPlayerContainer.HideImmediately();
        NGame.Instance.MainMenu?.EnableBackstopInstantly();
        NCapstoneContainer.Instance?.EnableBackstopInstantly();

        _feedbackScreen.SetScreenshot(screenshot);
        _stack.Push(_feedbackScreen);
    }

    public override void OnSubmenuClosed()
    {
        base.OnSubmenuClosed();

        ProcessMode = ProcessModeEnum.Disabled;
        SaveManager.Instance.SaveSettings();
        EmitSignal(SignalName.SettingsClosed);
    }

    protected override void OnSubmenuHidden()
    {
        base.OnSubmenuClosed();

        ProcessMode = ProcessModeEnum.Disabled;
        EmitSignal(SignalName.SettingsClosed);
    }

    protected override void OnSubmenuShown()
    {
        base.OnSubmenuShown();

        ProcessMode = ProcessModeEnum.Inherit;
        EmitSignal(SignalName.SettingsOpened);
    }

    public override void OnSubmenuOpened()
    {
        base.OnSubmenuOpened();

        ProcessMode = ProcessModeEnum.Inherit;
        EmitSignal(SignalName.SettingsOpened);
        _settingsTabManager.ResetTabs();
    }

    public override void OnFocusScreen()
    {
        _lastFocusedControl?.TryGrabFocus();
        _lastFocusedControl = null;
    }

    public override void OnUnfocusScreen()
    {
        _lastFocusedControl = GetViewport().GuiGetFocusOwner();
    }
}
