using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Nodes.Screens.ModdingScreen;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

public partial class NOpenModdingScreenButton : NButton
{
    private TextureRect _image = default!;
    private MegaLabel _label = default!;

    public override void _Ready()
    {
        ConnectSignals();
        _image = GetNode<TextureRect>("Image");
        _label = GetNode<MegaLabel>("Label");
        _label.Text = new LocString("settings_ui", "MODDING_SCREEN_BUTTON_LABEL").GetFormattedText();
    }

    protected override void OnDisable()
    {
        _image.Modulate = Colors.Gray;
    }

    protected override void OnEnable()
    {
        _image.Modulate = Colors.White;
    }

    protected override void OnFocus()
    {
        base.OnFocus(); // Sfx

        _image.Scale = Vector2.One * 1.05f;
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _image.Scale = Vector2.One;
    }
}
