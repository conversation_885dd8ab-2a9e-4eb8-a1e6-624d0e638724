using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Debug;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.InspectScreens;

/// <summary>
/// Allows the player to view a single relic. This allows players to see the higher resolution asset and flavor text.
/// </summary>
public partial class NInspectRelicScreen : Control, IFocusableScreen
{
    // Nodes
    private Control _popup = default!;
    private Control _backstop = default!;
    private MegaLabel _nameLabel = default!;
    private MegaLabel _rarityLabel = default!;
    private MegaRichTextLabel _description = default!;
    private MegaRichTextLabel _flavor = default!;
    private TextureRect _relicImage = default!;
    private ShaderMaterial _frameHsv = default!;
    private NGoldArrowButton _leftButton = default!;
    private NGoldArrowButton _rightButton = default!;

    // Anim
    private Tween? _screenTween;
    private Tween? _popupTween;
    private Vector2 _popupPosition;
    private float _leftButtonX;
    private float _rightButtonX;
    private const double _arrowButtonDelay = 0.1;

    // Data
    private IReadOnlyList<RelicModel> _relics = default!;
    private int _index;

    /// <summary>
    /// Opens the Relic Inspection Screen. Note that we don't need to Create() this
    /// screen because it's created and cached for re-use from game launch.
    /// </summary>
    /// <param name="relics">The collection of relics this relic is a part of (used for left/right arrows)</param>
    /// <param name="relic">The relic the user clicked on to open this screen (index is automatically identified)</param>
    public void Open(IReadOnlyList<RelicModel> relics, RelicModel relic)
    {
        Log.Info($"Inspecting Relic: {relic.Title}");

        _relics = relics;
        _index = relics.IndexOf(relic);
        SetRelic(_index);

        Visible = true;

        _popup.Modulate = StsColors.transparentBlack;
        _leftButton.Modulate = StsColors.transparentBlack;
        _rightButton.Modulate = StsColors.transparentBlack;

        _backstop.Visible = true;
        _backstop.MouseFilter = MouseFilterEnum.Stop;
        _leftButton.MouseFilter = MouseFilterEnum.Stop;
        _rightButton.MouseFilter = MouseFilterEnum.Stop;

        // Animate in the screen
        _screenTween?.Kill();
        _screenTween = CreateTween().SetParallel();
        _screenTween.TweenProperty(_backstop, "modulate:a", 0.9f, 0.25);
        _screenTween.TweenProperty(_leftButton, "position:x", _leftButtonX, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .From(_leftButtonX + 100f)
            .SetDelay(_arrowButtonDelay);
        _screenTween.TweenProperty(_leftButton, "modulate", Colors.White, 0.25)
            .SetDelay(_arrowButtonDelay);
        _screenTween.TweenProperty(_rightButton, "position:x", _rightButtonX, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .From(_rightButtonX - 100f)
            .SetDelay(_arrowButtonDelay);
        _screenTween.TweenProperty(_rightButton, "modulate", Colors.White, 0.25)
            .SetDelay(_arrowButtonDelay);

        // We need a separate Tween from the screen as players may paginate before the backdrop animates in
        _popupTween?.Kill();
        _popupTween = CreateTween().SetParallel();
        _popupTween.TweenProperty(_popup, "modulate", Colors.White, 0.25);
        _popupTween.TweenProperty(_popup, "position", _popupPosition, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic)
            .From(_popupPosition + new Vector2(0f, 200f));

        NControllerManager.Instance!.PushScreen(this);
    }

    public override void _Ready()
    {
        _popup = GetNode<Control>("%Popup");
        _backstop = GetNode<Control>("%Backstop");
        _nameLabel = GetNode<MegaLabel>("%RelicName");
        _rarityLabel = GetNode<MegaLabel>("%Rarity");
        _description = GetNode<MegaRichTextLabel>("%RelicDescription");
        _flavor = GetNode<MegaRichTextLabel>("%FlavorText");
        _relicImage = GetNode<TextureRect>("%RelicImage");
        _frameHsv = (ShaderMaterial)GetNode<Control>("%Frame").Material;
        _leftButton = GetNode<NGoldArrowButton>("%LeftArrow");
        _rightButton = GetNode<NGoldArrowButton>("%RightArrow");
        _popupPosition = _popup.Position;

        _backstop = GetNode<NButton>("Backstop");
        _backstop.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnBackstopPressed));

        _leftButton = GetNode<NGoldArrowButton>("LeftArrow");
        _leftButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnLeftButtonPressed));

        _rightButton = GetNode<NGoldArrowButton>("RightArrow");
        _rightButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnRightButtonPressed));

        _leftButtonX = _leftButton.Position.X;
        _rightButtonX = _rightButton.Position.X;
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (!IsVisibleInTree()) return;
        if (NDevConsole.Instance.Visible) return;
        if (GetViewport().GuiGetFocusOwner() is TextEdit or LineEdit) return;

        if (inputEvent.IsActionPressed(MegaInput.cancel))
        {
            TaskHelper.RunSafely(CloseScreen());
        }

        if (inputEvent.IsActionPressed(MegaInput.left))
        {
            OnLeftButtonPressed(_leftButton);
        }

        if (inputEvent.IsActionPressed(MegaInput.right))
        {
            OnRightButtonPressed(_rightButton);
        }
    }

    private void OnRightButtonPressed(NButton button)
    {
        SetRelic(_index + 1);

        _popup.Modulate = Colors.White;
        _popupTween?.Kill();
        _popupTween = CreateTween().SetParallel();
        _popupTween.TweenProperty(_popup, "position", _popupPosition, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(_popupPosition + new Vector2(100f, 0f));
    }

    private void OnLeftButtonPressed(NButton button)
    {
        SetRelic(_index - 1);

        _popup.Modulate = Colors.White;
        _popupTween?.Kill();
        _popupTween = CreateTween().SetParallel();
        _popupTween.TweenProperty(_popup, "position", _popupPosition, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(_popupPosition + new Vector2(-100f, 0f));
    }

    private void SetRelic(int index)
    {
        _index = Math.Clamp(index, 0, _relics.Count - 1);

        _leftButton.Visible = _index > 0;
        _leftButton.MouseFilter = _index > 0 ? MouseFilterEnum.Stop : MouseFilterEnum.Ignore;

        _rightButton.Visible = _index < _relics.Count - 1;
        _rightButton.MouseFilter = _index < _relics.Count - 1 ? MouseFilterEnum.Stop : MouseFilterEnum.Ignore;

        UpdateRelicDisplay();
    }

    /// <summary>
    /// Call to update the screen to match the relic we're viewing.
    /// Called on screen open and when paging left/right.
    /// </summary>
    private void UpdateRelicDisplay()
    {
        RelicModel relic = _relics[_index];

        if (SaveManager.Instance.IsRelicSeen(relic))
        {
            _nameLabel.Text = relic.Title.GetRawText();
            LocString rarityString = new("gameplay_ui", $"RELIC_RARITY.{relic.Rarity.ToString().ToUpper()}");
            _rarityLabel.Text = rarityString.GetFormattedText();
            _relicImage.SelfModulate = Colors.White;
            _description.SetTextAutoSize(relic.DynamicDescription.GetFormattedText());
            _flavor.SetTextAutoSize(relic.Flavor.GetFormattedText());
        }
        else
        {
            _nameLabel.Text = new LocString("inspect_relic_screen", "UNDISCOVERED_TITLE").GetFormattedText();
            _rarityLabel.Text = string.Empty;
            _relicImage.SelfModulate = StsColors.ninetyPercentBlack;
            _description.SetTextAutoSize(new LocString("inspect_relic_screen", "UNDISCOVERED_DESCRIPTION").GetFormattedText());
            _flavor.Text = string.Empty;
        }

        SetRarityVisuals(relic.Rarity);
        _relicImage.Texture = relic.BigIcon;
    }

    private void SetRarityVisuals(RelicRarity rarity)
    {
        // I used a Vector3 instead of 3 floats because the organization is prettier.
        Vector3 setHsv;

        switch (rarity)
        {
            case RelicRarity.Starter:
            // Fallthrough
            case RelicRarity.Common:
                _rarityLabel.Modulate = StsColors.cream;
                setHsv = new Vector3(0.95f, 0.25f, 0.9f); // Light Gray
                break;
            case RelicRarity.Uncommon:
                _rarityLabel.Modulate = StsColors.blue;
                setHsv = new Vector3(0.426f, 0.8f, 1.1f); // Light Blue
                break;
            case RelicRarity.Rare:
                _rarityLabel.Modulate = StsColors.gold;
                setHsv = new Vector3(1f, 0.8f, 1.15f); // Gold
                break;
            case RelicRarity.Shop:
                _rarityLabel.Modulate = StsColors.blue;
                setHsv = new Vector3(0.525f, 2.5f, 0.85f); // Merchant Blue
                break;
            case RelicRarity.Event:
                _rarityLabel.Modulate = StsColors.green;
                setHsv = new Vector3(0.23f, 0.75f, 0.9f); // Green
                break;
            case RelicRarity.Ancient:
                _rarityLabel.Modulate = StsColors.red;
                setHsv = new Vector3(0.875f, 3f, 0.9f); // Red
                break;
            case RelicRarity.None:
            // Fallthrough
            default:
                Log.Error($"Unspecified relic rarity: {rarity.ToString()}");
                throw new ArgumentOutOfRangeException();
        }

        _frameHsv.SetShaderParameter("h", setHsv.X);
        _frameHsv.SetShaderParameter("s", setHsv.Y);
        _frameHsv.SetShaderParameter("v", setHsv.Z);
    }

    private async Task CloseScreen()
    {
        _screenTween?.Kill();
        _screenTween = CreateTween().SetParallel();
        _screenTween.TweenProperty(_backstop, "modulate:a", 0f, 0.25);
        _screenTween.TweenProperty(_leftButton, "modulate:a", 0f, 0.1);
        _screenTween.TweenProperty(_rightButton, "modulate:a", 0f, 0.1);
        _screenTween.TweenProperty(_popup, "modulate", StsColors.transparentWhite, 0.1);

        _backstop.MouseFilter = MouseFilterEnum.Ignore;
        _leftButton.MouseFilter = MouseFilterEnum.Ignore;
        _rightButton.MouseFilter = MouseFilterEnum.Ignore;

        // TODO: Animate out the left/right arrows

        await ToSignal(_screenTween, Tween.SignalName.Finished);
        Visible = false;
        NControllerManager.Instance!.RemoveScreen(this);
    }

    private void OnBackstopPressed(NButton _)
    {
        TaskHelper.RunSafely(CloseScreen());
    }

    public void OnFocusScreen()
    {
        // Even though this screen doesn't have any nodes we want to focus right away
        // we want to make sure that we clear the controller focus from any previous screen.
        GetViewport().GuiReleaseFocus();
    }
    public void OnUnfocusScreen()
    {

    }
}
