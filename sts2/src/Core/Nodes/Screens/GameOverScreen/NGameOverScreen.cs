using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.RestSite;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;
using MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.GameOverScreen;

public partial class NGameOverScreen : NClickableControl, IOverlayScreen, IFocusableScreen
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/game_over_screen");

    // NOTE: Preload the death transition texture as it's huge
    public static IEnumerable<string> AssetPaths => [ScenePath];

    private ClimbState _climbState = default!;
    private ClimbHistory? _history;

    // Buttons
    private NGameOverContinueButton _continueButton = default!;
    private NViewClimbButton _viewClimbButton = default!;
    private NReturnToMainMenuButton _mainMenuButton = default!;
    private NGameOverContinueButton _leaderboardButton = default!;

    private NDailyClimbLeaderboard _leaderboard = default!;
    private Control _creatureContainer = default!;
    private NClimbSummary _summaryContainer = default!;
    private ColorRect _fullBlackBackstop = default!;
    private ColorRect _backstop = default!;
    private NMapPointHistory _mapPointHistory = default!;
    private NCommonBanner _banner = default!;
    private MegaRichTextLabel _deathQuote = default!;
    private Control _uiNode = default!;
    private Control _screenshakeContainer = default!; // Contains elements that should be shaken (excludes the backstop).
    private Label _discoveryLabel = default!;
    private string _encounterQuote = default!;

    // Screen transition animation
    private bool _isAnimatingSummary;
    private ShaderMaterial _backstopMaterial = default!;
    private Tween? _quoteTween;
    private Tween? _tween;

    public NetScreenType ScreenType => NetScreenType.GameOver;

    public override void _Ready()
    {
        _history = ClimbManager.Instance.History;
        _uiNode = GetNode<Control>("%Ui"); // NOTE: Bad name
        _continueButton = GetNode<NGameOverContinueButton>("%ContinueButton");
        _continueButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenSummaryScreen));
        _continueButton.Disable();

        _viewClimbButton = GetNode<NViewClimbButton>("%ViewClimbButton");
        _viewClimbButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenClimbHistoryScreen));

        _mainMenuButton = GetNode<NReturnToMainMenuButton>("%MainMenuButton");
        _mainMenuButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnMainMenuButtonPressed));

        _screenshakeContainer = GetNode<Control>("%ScreenshakeContainer");

        _leaderboardButton = GetNode<NGameOverContinueButton>("%LeaderboardButton");
        _leaderboardButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(ShowLeaderboard));

        _creatureContainer = GetNode<Control>("%CreatureContainer");
        _summaryContainer = GetNode<NClimbSummary>("%ClimbSummaryContainer");
        _backstop = GetNode<ColorRect>("%Backstop");
        _fullBlackBackstop = GetNode<ColorRect>("%FullBlackBackstop");
        _backstopMaterial = (ShaderMaterial)_backstop.Material;

        _leaderboard = GetNode<NDailyClimbLeaderboard>("%DailyClimbLeaderboard");

        _banner = GetNode<NCommonBanner>("%Banner");
        _banner.label.Text = "Death"; // TODO: Localize me based on death, victory, false victory, etc
        _discoveryLabel = GetNode<Label>("%DiscoveryLabel");
        _discoveryLabel.Text = new LocString("game_over_screen", "DISCOVERY_HEADER").GetFormattedText();

        _deathQuote = GetNode<MegaRichTextLabel>("%DeathQuoteLabel");
        _mapPointHistory = GetNode<NMapPointHistory>("%MapPointHistory");

        Connect(NClickableControl.SignalName.MouseReleased, Callable.From<InputEvent>(OnMouseReleased));

        // Uses our sneakily referenced ClimbHistoryEntry to create this quote.
        if (_history != null)
        {
            InitializeBannerAndQuote(_history);
        }
        else
        {
            Log.Warn("No climb history is available as the game is running in test mode or scene bootstrapper.");
            GetNode<Label>("%TestModeLabel").Visible = true;
        }
        NControllerManager.Instance!.PushScreen(this);
    }

    public override void _ExitTree()
    {
        NControllerManager.Instance!.RemoveScreen(this);
    }

    public override void _Input(InputEvent inputEvent)
    {
        bool wasDirectionalInput = inputEvent.IsActionPressed(MegaInput.left) || inputEvent.IsActionPressed(MegaInput.right) || inputEvent.IsActionPressed(MegaInput.up) || inputEvent.IsActionPressed(MegaInput.down);
        if (inputEvent.IsActionPressed(MegaInput.select) || wasDirectionalInput)
        {
            SpeedUpAnimatedRunSummary();
        }

        if (wasDirectionalInput)
        {
            if (!_isAnimatingSummary && GetViewport().GuiGetFocusOwner() == null)
            {
                // we defer the call here so that the input that triggered the focus doesnt end up getting handled again
                // by one of the map history nodes
                Callable.From(_mapPointHistory.OnFocus).CallDeferred();
            }
        }
    }

    private void OnMouseReleased(InputEvent _)
    {
        SpeedUpAnimatedRunSummary();
    }

    private void InitializeBannerAndQuote(ClimbHistory history)
    {
        if (history.Win)
        {
            _banner.label.Text = new LocString("game_over_screen", "BANNER.falseWin").GetFormattedText();
            _deathQuote.Text = string.Empty;
        }
        else
        {
            IReadOnlyList<LocString> deathBanners = LocManager.Instance.GetTable("game_over_screen").GetLocStringsWithPrefix("BANNER.lose");
            _banner.label.Text = deathBanners[Rng.Chaotic.NextInt(0, deathBanners.Count)].GetFormattedText();

            IReadOnlyList<LocString> deathQuotes = LocManager.Instance.GetTable("game_over_screen").GetLocStringsWithPrefix("QUOTES");
            _deathQuote.Text = $"[center]{deathQuotes[Rng.Chaotic.NextInt(0, deathQuotes.Count)].GetFormattedText()}[/center]";
            // TODO: These quotes are agnostic. Mix in the character-specific death quotes.
        }

        // Cache the quote we'll show in the summary here.
        _encounterQuote = $"[center]{NClimbHistory.GetDeathQuote(history,
            LocalContext.GetMe(_climbState)!.Character.Id,
            NClimbHistory.GetGameOverType(history))}[/center]";
    }

    private async Task AnimateInQuote()
    {
        if (_deathQuote.Modulate.A != 0f)
        {
            _quoteTween?.Kill();
            _quoteTween = CreateTween();
            _quoteTween.TweenProperty(_deathQuote, "modulate:a", 0f, 0.25);
            await ToSignal(_quoteTween, Tween.SignalName.Finished);
            _deathQuote.Text = _encounterQuote;
            _quoteTween.Kill();
            await Task.Delay(1000);
        }

        _quoteTween?.Kill();
        _quoteTween = CreateTween().SetParallel();
        _quoteTween.TweenProperty(_deathQuote, "position:y", 156f, 2.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(90f);
        _quoteTween.TweenProperty(_deathQuote, "modulate:a", 1f, 1.5);
    }

    /// <summary>
    /// Create an instance of this screen.
    /// Null if we're in test mode.
    /// </summary>
    public static NGameOverScreen? Create(ClimbState climbState)
    {
        if (TestMode.IsOn) return null;

        NGameOverScreen node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NGameOverScreen>();
        node._climbState = climbState;

        return node;
    }

    private void OpenSummaryScreen(NButton _)
    {
        _isAnimatingSummary = true;
        _continueButton.Disable();

        // Darkens the creatures so you can view the Summary screen stuff better
        Tween tween = CreateTween();
        tween.TweenProperty(_creatureContainer, "modulate", new Color(0.25f, 0.15f, 0.15f), 0.5);

        TaskHelper.RunSafely(AnimateInQuote());
        TaskHelper.RunSafely(AnimateRunSummary());
    }

    private async Task AnimateRunSummary()
    {
        if (_history == null) throw new InvalidOperationException("History must be set before animating in run summary!");

        _summaryContainer.Visible = true;
        _mapPointHistory.LoadHistory(_history);
        _mapPointHistory.SetPlayer(_history.Players.First(p => p.Id == LocalContext.NetId!.Value));
        _mapPointHistory.InitForAnimation();

        await _mapPointHistory.AnimateIn();
        await AnimateDiscoveries();

        if (_history!.GameMode == GameMode.Daily)
        {
            // Initialize leaderboard early so results are available by the time the leaderboard button is pressed
            _leaderboard.Initialize(ClimbManager.Instance.DailyTime!.Value, _climbState.Players.Select(p => p.NetId), false);

            _leaderboardButton.Visible = true;
            _leaderboardButton.Enable();
        }
        else
        {
            _mainMenuButton.Visible = true;
            _mainMenuButton.Enable();
        }

        // TODO: This feature is not yet implemented
        // _viewClimbClimbButton.Visible = true;
        // _viewClimbButton.Enable();
    }

    /// <summary>
    /// For people who don't have time to watch this summary at normal speed >:(
    /// </summary>
    private void SpeedUpAnimatedRunSummary()
    {
        if (!_isAnimatingSummary) return;

        _isAnimatingSummary = false;
        _summaryContainer.HurryUp();
        _mapPointHistory.HurryUp();

        _tween?.Kill();
    }

    private void ShowLeaderboard(NButton _)
    {
        Tween tween = CreateTween().SetParallel();
        _leaderboard.Modulate = _leaderboard.Modulate with { A = 0f };
        tween.TweenProperty(_leaderboard, "modulate:a", 1f, 0.5f);
        tween.TweenProperty(_summaryContainer, "modulate:a", 0f, 0.5f);
        tween.TweenProperty(_deathQuote, "modulate:a", 0f, 0.5f);

        _leaderboard.Visible = true;
        _leaderboardButton.Disable();

        _mainMenuButton.Visible = true;
        _mainMenuButton.Enable();
    }

    private async Task AnimateDiscoveries()
    {
        await _summaryContainer.AnimateInDiscoveries(_climbState);
        // TODO: Animate in main menu button after awaiting summary container to be done.
        _isAnimatingSummary = false;
    }

    private void OpenClimbHistoryScreen(NButton _)
    {
        // 1. Hide UI elements on game over screen.
        // 2. Disable this and the return to main menu button.
        // 3. Open the ClimbHistoryScreen with special flags.

        Control climbHistoryScreen = ResourceLoader.Load<PackedScene>("res://scenes/screens/climb_history_screen/climb_history_screen_via_game_over_screen.tscn").Instantiate<Control>();
        this.AddChildSafely(climbHistoryScreen);
    }

    private void OnMainMenuButtonPressed(NButton _)
    {
        ReturnToMainMenu();
    }

    private void ReturnToMainMenu()
    {
        // See the check in Climb.LocalPlayerDisconnected for why this is necessary.
        if (ClimbManager.Instance.NetService.Type == NetGameType.Host)
        {
            ClimbManager.Instance.NetService.Disconnect(NetError.QuitGameOver);
        }

        _mainMenuButton.Disable();
        TaskHelper.RunSafely(TransitionOut());
    }

    private async Task TransitionOut()
    {
        await NGame.Instance!.ReturnToMainMenuAfterClimb();
    }

    public void AfterOverlayOpened()
    {
        MoveCreaturesToDifferentLayerAndDisableUi();
        TaskHelper.RunSafely(AnimateIn());
    }

    /// <summary>
    /// We move the Creature nodes so they render above the game over screen backstop!
    /// Drama!
    /// </summary>
    private void MoveCreaturesToDifferentLayerAndDisableUi()
    {
        List<NCreature> creatures;
        List<NCreatureVisuals> creatureVisuals;

        if (NCombatRoom.Instance != null)
        {
            // Hide things like your hand, energy icon, card pile UIs, etc
            NCombatRoom.Instance.Ui.AnimOut((CombatRoom)_climbState.CurrentRoom!);
            creatures = NCombatRoom.Instance.CreatureNodes.ToList();
            creatureVisuals = creatures.Select(c => c.Visuals).ToList();
        }
        else if (NVictoryRoom.Instance != null)
        {
            creatures = NVictoryRoom.Instance.CreatureNodes.ToList();
            creatureVisuals = creatures.Select(c => c.Visuals).ToList();
        }
        else if (NFinishedCombatRoom.Instance != null)
        {
            creatures = NFinishedCombatRoom.Instance.CreatureNodes.ToList();
            creatureVisuals = creatures.Select(c => c.Visuals).ToList();
        }
        else if (NMerchantRoom.Instance != null)
        {
            creatures = [];
            creatureVisuals = NMerchantRoom.Instance.PlayerVisuals;

            foreach (NCreatureVisuals visuals in creatureVisuals)
            {
                visuals.SpineBody!.GetAnimationState().SetAnimation("die", false);
            }
        }
        else if (NRestSiteRoom.Instance != null)
        {
            // The rest site uses its own visuals with separate spine files & animations. Spawn new visuals at the same
            // location and approximately the same size, then animate them to die
            creatures = [];
            creatureVisuals = [];

            foreach (Player player in _climbState.Players)
            {
                NCreatureVisuals visuals = player.Creature.CreateVisuals()!;
                creatureVisuals.Add(visuals);
                _creatureContainer.AddChildSafely(visuals);
                visuals.SpineBody!.GetAnimationState().SetAnimation("die", false);

                NRestSiteCharacter character = NRestSiteRoom.Instance.GetCharacterForPlayer(player)!;
                visuals.GlobalPosition = character.GlobalPosition;
                visuals.Scale = character.Scale;
                character.Visible = false;

                // The origin of the rest site character is on the log so scoot forward a bit
                Vector2 scoot = new(100f, 100f);
                visuals.Position += scoot * new Vector2(Math.Sign(visuals.Scale.X), Math.Sign(visuals.Scale.Y));
            }
        }
        else
        {
            // If we are in a room that has no creatures in it, then make some visuals for the players
            creatures = [];
            creatureVisuals = [];

            foreach (Player player in _climbState.Players)
            {
                NCreatureVisuals visuals = player.Creature.CreateVisuals()!;
                creatureVisuals.Add(visuals);
                _creatureContainer.AddChildSafely(visuals);
                visuals.SpineBody!.GetAnimationState().SetAnimation("die", false);
            }

            float playerPadding = Math.Min(250f, (Size.X - 200f) / (creatureVisuals.Count - 1));
            float xPos = (creatureVisuals.Count - 1) * -playerPadding * 0.5f;

            // Align players in a row, centered on the screen
            foreach (NCreatureVisuals creature in creatureVisuals)
            {
                creature.Position = _creatureContainer.Size * 0.5f + new Vector2(xPos, 200f);
                xPos += playerPadding;
            }
        }

        // Tries to sort creatures by their previous hierarchy orders. This breaks between player creatures and
        // monster creatures since they dont share a parent node, but in theory this is fine because those two
        // groups never touch.
        creatures.Sort((c1, c2) => c1.GetIndex().CompareTo(c2.GetIndex()));

        // Hide intents and health bars of creatures
        foreach (NCreature creature in creatures)
        {
            creature.AnimHideIntent();
            creature.AnimDisableUi();
        }

        foreach (NCreatureVisuals visuals in creatureVisuals)
        {
            visuals.Reparent(_creatureContainer);
        }
    }

    /// <summary>
    /// Called when the Game Over screen appears (immediately upon Death/Victory)
    /// </summary>
    private async Task AnimateIn()
    {
        // NOTE: Do different stuff in test mode?

        _tween = CreateTween();
        _uiNode.Modulate = StsColors.transparentWhite;

        // If we're in an event room, quickly animate to black first and fade in characters at the same time
        if (NEventRoom.Instance != null)
        {
            _fullBlackBackstop.Modulate = _fullBlackBackstop.Modulate with { A = 0f };
            _fullBlackBackstop.Visible = true;
            _tween.TweenProperty(_fullBlackBackstop, "modulate:a", 1f, 0.2);

            foreach (NCreatureVisuals visuals in _creatureContainer.GetChildren().OfType<NCreatureVisuals>())
            {
                visuals.Modulate = visuals.Modulate with { A = 0f };
                _tween.Parallel().TweenProperty(visuals, "modulate:a", 1f, 0.2);
            }
        }

        _tween.TweenMethod(Callable.From<float>(UpdateBackstopMaterial),
                _backstopMaterial.GetShaderParameter("threshold"), 1f, 1.5)
            .SetEase(Tween.EaseType.InOut)
            .SetTrans(Tween.TransitionType.Sine);

        await ToSignal(_tween, Tween.SignalName.Finished);
        _banner.AnimateIn();

        _tween?.Kill();
        _tween = CreateTween();
        _tween.TweenProperty(_uiNode, "modulate:a", 1f, 0.25);
        await ToSignal(_tween, Tween.SignalName.Finished);

        _ = TaskHelper.RunSafely(AnimateInQuote());
        _continueButton.Enable();
    }

    private void UpdateBackstopMaterial(float value)
    {
        _backstopMaterial.SetShaderParameter("threshold", value);
    }

    public void AfterOverlayClosed()
    {
        this.QueueFreeSafely();
    }

    public void AfterOverlayShown()
    {
        NGame.Instance!.SetScreenShakeTarget(_screenshakeContainer);
        Visible = true;
    }

    public void AfterOverlayHidden()
    {
        Visible = false;
    }

    public bool UseSharedBackstop => false;
    public void OnFocusScreen()
    {
        // Even though this screen doesn't have any nodes we want to focus right away
        // we want to make sure that we clear focus from any previous screen.
        GetViewport().GuiReleaseFocus();
    }
    public void OnUnfocusScreen()
    {

    }
}
