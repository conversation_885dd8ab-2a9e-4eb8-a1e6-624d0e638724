using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CardSelection;

/// <summary>
/// A generic select from the cards in the player's deck. Managing logic for the extra confirmation screen.
/// Typically we use this screen when we are modifying the composition of a deck rather than a specific card.
/// This is included but not limited to:
/// - transforming cards
/// - removing cards
/// - cloning cards (<PERSON>'s Mirror)
/// Does not include actions that modify specific cards such as,
/// upgrading (see NDeckSelectScreen) or applying enchantments (see NDeckEnchantSelectScreen) to cards, as those have special UI
/// </summary>
public sealed partial class NDeckCardSelectScreen : NCardGridSelectionScreen
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/card_selection/deck_card_select_screen");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    private readonly HashSet<CardModel> _selectedCards = [];
    private CardSelectorPrefs _prefs;

    private Control _previewContainer = default!;
    private Control _previewCards = default!;
    private NBackButton _previewCancelButton = default!;
    private NConfirmButton _previewConfirmButton = default!;
    private NBackButton _closeButton = default!;
    private NConfirmButton _confirmButton = default!;
    private MegaRichTextLabel _infoLabel = default!;

    protected override IEnumerable<Control> PeekButtonTargets =>
    [
        _previewContainer,
        _closeButton,
        _confirmButton,
    ];

    public override void _Ready()
    {
        ConnectSignals();
        _previewContainer = GetNode<Control>("%PreviewContainer");
        _previewCards = _previewContainer.GetNode<Control>("%Cards");
        _previewCancelButton = _previewContainer.GetNode<NBackButton>("%PreviewCancel");
        _previewConfirmButton = _previewContainer.GetNode<NConfirmButton>("%PreviewConfirm");
        _closeButton = GetNode<NBackButton>("%Close");
        _confirmButton = GetNode<NConfirmButton>("%Confirm");
        _infoLabel = GetNode<MegaRichTextLabel>("%BottomLabel");

        _previewCancelButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CancelSelection));
        _previewConfirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(ConfirmSelection));
        _closeButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseSelection));
        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(PreviewSelection));

        if (_prefs.Cancelable)
        {
            _closeButton.Enable();
        }
        else
        {
            _closeButton.Disable();
        }

        RefreshConfirmButtonVisibility();

        _previewContainer.Visible = false;
        _previewContainer.MouseFilter = MouseFilterEnum.Ignore;

        _previewCancelButton.Disable();
        _previewConfirmButton.Disable();
        _infoLabel.Text = $"[center]{_prefs.Prompt.GetFormattedText()}[/center]";
    }

    public static NDeckCardSelectScreen Create(IReadOnlyList<CardModel> cards, CardSelectorPrefs prefs)
    {
        NDeckCardSelectScreen node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NDeckCardSelectScreen>();
        node.Name = nameof(NDeckCardSelectScreen);
        node._cards = cards;
        node._prefs = prefs;

        return node;
    }

    private void RefreshConfirmButtonVisibility()
    {
        // If min & max select are the same, we auto-confirm upon selecting
        if (_prefs.MinSelect != _prefs.MaxSelect && _selectedCards.Count >= _prefs.MinSelect)
        {
            _confirmButton.Enable();
        }
        else
        {
            _confirmButton.Disable();
        }
    }

    protected override void OnCardClicked(CardModel card)
    {
        if (_selectedCards.Add(card))
        {
            _grid.HighlightCard(card);

            if (_prefs.MaxSelect == _selectedCards.Count)
            {
                PreviewSelection();
            }
        }
        else
        {
            _selectedCards.Remove(card);
            _grid.UnhighlightCard(card);
        }

        RefreshConfirmButtonVisibility();
    }

    private void PreviewSelection(NButton _)
    {
        PreviewSelection();
    }

    private void PreviewSelection()
    {
        GetViewport().GuiReleaseFocus();
        _previewContainer.Visible = true;
        _previewContainer.MouseFilter = MouseFilterEnum.Stop;
        _closeButton.Disable();
        _grid.SetCanScroll(false);

        _previewCancelButton.Enable();
        _previewConfirmButton.Enable();

        foreach (CardModel selectedCard in _selectedCards)
        {
            _grid.UnhighlightCard(selectedCard);
            NCard node = NCard.Create(selectedCard)!;
            NPreviewCardHolder previewCardHolder = NPreviewCardHolder.Create(node, true, false)!;
            _previewCards.AddChildSafely(previewCardHolder);
            node.UpdateVisuals(selectedCard.Pile!.Type);
        }

        // defer the call here so that all card nodes are added
        // and the size of the container is set up before we rescale it
        Callable.From(() =>
        {
            _previewCards.PivotOffset = _previewCards.Size / 2;
            float scale = 1f;
            if (_selectedCards.Count > 6)
            {
                scale = 0.55f;
            }
            else if (_selectedCards.Count > 3)
            {
                scale = 0.8f;
            }

            _previewCards.Scale = Vector2.One * scale;
        }).CallDeferred();
    }

    private void CloseSelection(NButton _)
    {
        _completionSource.SetResult(Array.Empty<CardModel>());
        NOverlayStack.Instance!.Remove(this);
    }

    private void CancelSelection(NButton _)
    {
        _previewContainer.Visible = false;
        _previewCancelButton.Disable();
        _previewConfirmButton.Disable();

        _grid.SetCanScroll(true);
        _previewContainer.MouseFilter = MouseFilterEnum.Ignore;

        for (int i = 0; i < _previewCards.GetChildCount(); i++)
        {
            _previewCards.GetChild(i).QueueFreeSafely();
        }

        // not a huge fan of having to do a different pattern here, but we have to do this because the preview container
        // isn't its own screen
        _grid.GetCardHolder(_selectedCards.Last())?.TryGrabFocus();
        _selectedCards.Clear();

        if (_prefs.Cancelable)
        {
            _closeButton.Enable();
        }
    }

    private void ConfirmSelection(NButton _)
    {
        CheckIfSelectionComplete();
    }

    private void CheckIfSelectionComplete()
    {
        if (_selectedCards.Count < _prefs.MinSelect) return;

        _completionSource.SetResult(_selectedCards);
        NOverlayStack.Instance!.Remove(this);
    }

    public override void OnFocusScreen()
    {
        // not a huge fan of having to do a different pattern here, but we have to do this because the preview container
        // isn't its own screen
        if (!_previewContainer.Visible)
        {
            _grid.OnFocus();
        }
    }

    public override void AfterOverlayShown()
    {
        if (_prefs.Cancelable)
        {
            _closeButton.Enable();
        }
    }

    public override void AfterOverlayHidden()
    {
        _closeButton.Disable();
    }
}
