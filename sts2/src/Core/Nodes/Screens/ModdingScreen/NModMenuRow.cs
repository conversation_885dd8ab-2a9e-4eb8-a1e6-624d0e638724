using System;
using System.Text;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Modding;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ModdingScreen;

public partial class NModMenuRow : NClickableControl
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("screens/modding/modding_screen_row");
    private const float _selectedAlpha = 0.25f;

    private Panel _selectionHighlight = default!;
    private NTickbox _tickbox = default!;
    private NModdingScreen _screen = default!;

    public Mod? Mod { get; private set; }
    private bool _isSelected;

    public static NModMenuRow? Create(NModdingScreen screen, Mod mod)
    {
        if (TestMode.IsOn) return null;
        NModMenuRow row = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NModMenuRow>();
        row.Mod = mod;
        row._screen = screen;
        return row;
    }

    public override void _Ready()
    {
        // Null if it's the placeholder row in the scene
        if (Mod == null) return;

        _selectionHighlight = GetNode<Panel>("SelectionHighlight");
        NTickbox tickbox = GetNode<NTickbox>("Tickbox");
        MegaRichTextLabel title = GetNode<MegaRichTextLabel>("Title");
        TextureRect platformIcon = GetNode<TextureRect>("PlatformIcon");

        _selectionHighlight.Modulate = _selectionHighlight.Modulate with { A = 0f };

        tickbox.IsTicked = !(SaveManager.Instance.SettingsSave.ModSettings?.IsModDisabled(Mod) ?? false);
        tickbox.Connect(NTickbox.SignalName.Toggled, Callable.From<NTickbox>(OnTickboxToggled));

        title.Text = Mod.manifest?.name ?? Mod.pckName;
        platformIcon.Texture = GetPlatformIcon(Mod.modSource);

        title.Modulate = Mod.wasLoaded ? Colors.White : StsColors.gray;
        platformIcon.Modulate = Mod.wasLoaded ? Colors.White : StsColors.gray;

        ConnectSignals();
    }

    protected override void OnFocus()
    {
        if (_isSelected) return;

        _selectionHighlight.Modulate = StsColors.darkBlue with { A = _selectedAlpha };
    }

    protected override void OnUnfocus()
    {
        if (_isSelected) return;

        _selectionHighlight.Modulate = Colors.Transparent;
    }

    protected override void OnRelease()
    {
        _screen.OnRowSelected(this);
    }

    public void SetSelected(bool isSelected)
    {
        if (_isSelected != isSelected)
        {
            _isSelected = isSelected;

            if (_isSelected)
            {
                _selectionHighlight.Modulate = StsColors.blue with { A = _selectedAlpha };
            }
            else if (IsFocused)
            {
                _selectionHighlight.Modulate = StsColors.darkBlue with { A = _selectedAlpha };
            }
            else
            {
                _selectionHighlight.Modulate = Colors.Transparent;
            }
        }
    }

    private void OnTickboxToggled(NTickbox tickbox)
    {
        SaveManager.Instance.SettingsSave.ModSettings ??= new ModSettings();

        if (tickbox.IsTicked)
        {
            SaveManager.Instance.SettingsSave.ModSettings.DisabledMods.RemoveAll(m => m.Name == Mod!.pckName && m.Source == Mod.modSource);
        }
        else
        {
            SaveManager.Instance.SettingsSave.ModSettings.DisabledMods.Add(new DisabledMod(Mod!));
        }

        _screen.OnModEnabledOrDisabled();
    }

    public static Texture2D GetPlatformIcon(ModSource modSource)
    {
        return PreloadManager.Cache.GetTexture2D(modSource switch
        {
            ModSource.ModsDirectory => ImageHelper.GetImagePath("ui/mods/folder.png"),
            ModSource.SteamWorkshop => ImageHelper.GetImagePath("ui/mods/steam_logo.png"),
            _ => throw new ArgumentOutOfRangeException(nameof(modSource), modSource, null)
        });
    }
}
