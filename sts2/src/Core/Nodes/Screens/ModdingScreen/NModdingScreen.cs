using System.Linq;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Modding;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves;
using Steamworks;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ModdingScreen;

public partial class NModdingScreen : NSubmenu
{
    private NModInfoContainer _modInfoContainer = default!;
    private Control _modRowContainer = default!;
    private Control _pendingChangesWarning = default!;

    public override void _Ready()
    {
        _modInfoContainer = GetNode<NModInfoContainer>("%ModInfoContainer");
        _modRowContainer = GetNode<Control>("%ModsScrollContainer/Mask/Content");
        _pendingChangesWarning = GetNode<Control>("%PendingChangesLabel");

        NButton getModsButton = GetNode<NButton>("%GetModsButton");
        NButton makeModsButton = GetNode<NButton>("%MakeModsButton");

        foreach (Node child in _modRowContainer.GetChildren())
        {
            child.QueueFreeSafely();
        }

        foreach (Mod mod in ModManager.AllMods)
        {
            OnNewModDetected(mod);
        }

        getModsButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnGetModsPressed));
        makeModsButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnMakeModsPressed));

        getModsButton.GetNode<MegaLabel>("Visuals/Label").SetTextAutoSize(new LocString("settings_ui", "MODDING_SCREEN.GET_MODS_BUTTON").GetFormattedText());
        makeModsButton.GetNode<MegaLabel>("Visuals/Label").SetTextAutoSize(new LocString("settings_ui", "MODDING_SCREEN.MAKE_MODS_BUTTON").GetFormattedText());
        GetNode<MegaRichTextLabel>("%InstalledModsTitle").SetTextAutoSize(new LocString("settings_ui", "MODDING_SCREEN.INSTALLED_MODS_TITLE").GetFormattedText());
        GetNode<MegaRichTextLabel>("%PendingChangesLabel").SetTextAutoSize(new LocString("settings_ui", "MODDING_SCREEN.PENDING_CHANGES_WARNING").GetFormattedText());

        _pendingChangesWarning.Visible = false;
        ModManager.OnModDetected += OnNewModDetected;

        ConnectSignals();
    }

    private void OnGetModsPressed(NButton _)
    {
        PlatformUtil.OpenUrl("https://steamcommunity.com/app/2868840/workshop/");
    }

    private void OnMakeModsPressed(NButton _)
    {
        PlatformUtil.OpenUrl("https://gitlab.com/megacrit/sts2/example-mod/-/wikis/home");
    }

    public void OnRowSelected(NModMenuRow row)
    {
        row.SetSelected(true);
        _modInfoContainer.Fill(row.Mod!);

        foreach (NModMenuRow otherRow in _modRowContainer.GetChildren().OfType<NModMenuRow>())
        {
            if (otherRow != row)
            {
                otherRow.SetSelected(false);
            }
        }
    }

    /// <summary>
    /// Called both during initialization and when a new mod is installed during runtime.
    /// </summary>
    private void OnNewModDetected(Mod mod)
    {
        NModMenuRow row = NModMenuRow.Create(this, mod)!;
        _modRowContainer.AddChildSafely(row);
        OnModEnabledOrDisabled();
    }

    public void OnModEnabledOrDisabled()
    {
        foreach (Mod mod in ModManager.AllMods)
        {
            bool isDisabled = SaveManager.Instance.SettingsSave.ModSettings?.IsModDisabled(mod) ?? false;

            // If a mod was loaded and is now disabled, or it wasn't loaded and is now enabled, warn player that they'll
            // need to restart the game
            if (mod.wasLoaded == isDisabled)
            {
                _pendingChangesWarning.Visible = true;
                return;
            }
        }

        _pendingChangesWarning.Visible = false;
    }
}
