using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ModdingScreen;

/// <summary>
/// Ping button. Flies in from off-screen after a short delay when the local player is ready but remote players are not.
/// </summary>
public partial class NModMenuButton : NButton
{
    private Control _visuals = default!;
    private TextureRect _image = default!;
    private Label _label = default!;
    private Viewport _viewport = default!;
    private ShaderMaterial _hsv = default!;

    private Tween? _hoverTween;

    public override void _Ready()
    {
        ConnectSignals();

        _visuals = GetNode<Control>("Visuals");
        _image = GetNode<TextureRect>("Visuals/Image");
        _label = GetNode<Label>("Visuals/Label");
        _viewport = GetViewport();
        _hsv = (ShaderMaterial)_image.Material;

        LocString loc = new("gameplay_ui", "PING_BUTTON");
        _label.Text = loc.GetFormattedText();
    }

    protected override void OnRelease()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), IsFocused ? 1.5f : 1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_visuals, "position", Vector2.Zero, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_label, "modulate", IsEnabled ? StsColors.cream : StsColors.gray, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnFocus()
    {
        base.OnFocus();

        _hoverTween?.Kill();
        _hsv.SetShaderParameter("v", 1.5);
        _visuals.Position = new Vector2(0f, -2f);
    }

    protected override void OnUnfocus()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_visuals, "position", Vector2.Zero, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_label, "modulate", IsEnabled ? StsColors.cream : StsColors.gray, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnPressDown()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_visuals, "position", new Vector2(0f, 4f), 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        _hoverTween.TweenProperty(_label, "modulate", Colors.DarkGray, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    private void UpdateShaderV(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }
}
