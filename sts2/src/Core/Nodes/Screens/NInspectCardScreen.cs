using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Debug;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.Sts2.Core.Nodes.Screens;

/// <summary>
/// Shows a very large version of a single card so players can view the portrait and its upgrade.
/// Accessed from various card pile screens, shop, climb history screen, and card library.
/// We have to do this, rather than making it its own Overlay Screen, because we end up using
/// it both for other overlay screens and capstone screens (deck view screen).
/// </summary>
public partial class NInspectCardScreen : Control, IFocusableScreen
{
    private NCard _card = default!;
    private NButton _backstop = default!;
    private NTickbox _upgradeTickbox = default!;
    private NButton _leftButton = default!;
    private NButton _rightButton = default!;
    private Control _hoverTipRect = default!;

    private List<CardModel>? _cards;
    private int _index;

    private Tween? _openTween;
    private Tween? _cardTween;
    private Vector2 _cardPosition;
    private float _leftButtonX;
    private float _rightButtonX;
    private const double _arrowButtonDelay = 0.1;

    private bool IsShowingUpgradedCard => _upgradeTickbox.IsTicked || !_upgradeTickbox.Visible;

    public override void _Ready()
    {
        _card = GetNode<NCard>("Card");
        _cardPosition = _card.Position;
        _hoverTipRect = GetNode<Control>("HoverTipRect");

        _backstop = GetNode<NButton>("Backstop");
        _backstop.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnBackstopPressed));

        _leftButton = GetNode<NButton>("LeftArrow");
        _leftButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnLeftButtonReleased));

        _rightButton = GetNode<NButton>("RightArrow");
        _rightButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnRightButtonReleased));

        _leftButtonX = _leftButton.Position.X;
        _rightButtonX = _rightButton.Position.X;

        _upgradeTickbox = GetNode<NTickbox>("%Upgrade");
        _upgradeTickbox.IsTicked = false;
        _upgradeTickbox.Connect(NTickbox.SignalName.Toggled, Callable.From<NTickbox>(ToggleShowUpgrade));

        HidePreview();
    }

    public void Open(List<CardModel> cards, int index, bool startUpgraded = false)
    {
        _cards = cards;

        Visible = true;
        MouseFilter = MouseFilterEnum.Stop;
        _upgradeTickbox.IsTicked = startUpgraded;
        SetCard(index);

        _card.Scale = Vector2.One * 1.75f;
        _card.Modulate = StsColors.transparentBlack;
        _leftButton.Modulate = StsColors.transparentBlack;
        _rightButton.Modulate = StsColors.transparentBlack;

        _openTween?.Kill();
        _openTween = CreateTween().SetParallel();
        _openTween.TweenProperty(_backstop, "modulate:a", 0.9f, 0.25);
        _openTween.TweenProperty(this, "modulate:a", 1f, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(0f);
        _openTween.TweenProperty(_leftButton, "position:x", _leftButtonX, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .From(_leftButtonX + 100f)
            .SetDelay(_arrowButtonDelay);
        _openTween.TweenProperty(_leftButton, "modulate", Colors.White, 0.25)
            .SetDelay(_arrowButtonDelay);
        _openTween.TweenProperty(_rightButton, "position:x", _rightButtonX, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .From(_rightButtonX - 100f)
            .SetDelay(_arrowButtonDelay);
        _openTween.TweenProperty(_rightButton, "modulate", Colors.White, 0.25)
            .SetDelay(_arrowButtonDelay);

        _cardTween?.Kill();
        _cardTween = CreateTween().SetParallel();
        _cardTween.TweenProperty(_card, "modulate", Colors.White, 0.25);
        _cardTween.TweenProperty(_card, "scale", Vector2.One * 2f, 0.15)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Spring)
            .SetDelay(0.1);

        NControllerManager.Instance!.PushScreen(this);
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (!IsVisibleInTree()) return;
        if (NDevConsole.Instance.Visible) return;
        if (GetViewport().GuiGetFocusOwner() is TextEdit or LineEdit) return;

        if (inputEvent.IsActionPressed(MegaInput.cancel))
        {
            TaskHelper.RunSafely(CloseScreen());
        }

        if (inputEvent.IsActionPressed(MegaInput.left))
        {
            OnLeftButtonReleased(_leftButton);
        }

        if (inputEvent.IsActionPressed(MegaInput.right))
        {
            OnRightButtonReleased(_rightButton);
        }


    }

    private void OnRightButtonReleased(NButton _)
    {
        SetCard(_index + 1);

        _card.Modulate = Colors.White;
        _openTween?.Kill();
        _openTween = CreateTween().SetParallel();
        _openTween.TweenProperty(_card, "position", _cardPosition, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(_cardPosition + new Vector2(100f, 0f));
    }

    private void OnLeftButtonReleased(NButton _)
    {
        SetCard(_index - 1);

        _card.Modulate = Colors.White;
        _openTween?.Kill();
        _openTween = CreateTween().SetParallel();
        _openTween.TweenProperty(_card, "position", _cardPosition, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(_cardPosition + new Vector2(-100f, 0f));
    }

    private void ToggleShowUpgrade(NTickbox _)
    {
        UpdateCardDisplay();
    }

    private void UpdateCardDisplay()
    {
        CardModel baseCard = _cards![_index];
        CardModel displayedCard;

        if (IsShowingUpgradedCard && !baseCard.IsUpgraded && baseCard.IsUpgradable)
        {
            // Normally we use ToMutable() (which only works on Canonical models) or ClonePreservingMutability(), but
            // this is a very general node that's used in both mutable (upgrade selection) and canonical (card reward)
            // spots, and we want a mutable clone regardless of what we started with.
            CardModel upgradedCard = (CardModel)_cards![_index].MutableClone();
            upgradedCard.UpgradePreviewType = CardUpgradePreviewType.Deck;
            upgradedCard.UpgradeInternal();
            _card.Model = upgradedCard;
            _card.ShowUpgradePreview();
            displayedCard = upgradedCard;
        }
        else
        {
            _card.Model = baseCard;
            _card.UpdateVisuals(CardPileTarget.None);
            displayedCard = baseCard;
        }

        NHoverTipSet.Clear();
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, displayedCard.HoverTips);
        tip.SetAlignment(_hoverTipRect, HoverTip.GetHoverTipAlignment(this));
    }

    private void SetCard(int index)
    {
        _index = Math.Clamp(index, 0, _cards!.Count - 1);

        _leftButton.Visible = _index > 0;
        _leftButton.MouseFilter = _index > 0 ? MouseFilterEnum.Stop : MouseFilterEnum.Ignore;

        _rightButton.Visible = _index < _cards.Count - 1;
        _rightButton.MouseFilter = _index < _cards.Count - 1 ? MouseFilterEnum.Stop : MouseFilterEnum.Ignore;

        bool showUpgradeTickbox = !_cards![_index].IsUpgraded && _cards![_index].IsUpgradable;
        _upgradeTickbox.Visible = showUpgradeTickbox;
        _upgradeTickbox.MouseFilter = showUpgradeTickbox ? MouseFilterEnum.Stop : MouseFilterEnum.Ignore;

        UpdateCardDisplay();
    }

    private void OnBackstopPressed(NButton _) => HidePreview();

    private void HidePreview()
    {
        MouseFilter = MouseFilterEnum.Ignore;
        _leftButton.MouseFilter = MouseFilterEnum.Ignore;
        _rightButton.MouseFilter = MouseFilterEnum.Ignore;

        NHoverTipSet.Clear();
        TaskHelper.RunSafely(CloseScreen());
        SetProcessUnhandledInput(false);
    }

    private async Task CloseScreen()
    {
        _openTween?.Kill();
        _openTween = CreateTween().SetParallel();
        _openTween.TweenProperty(_backstop, "modulate:a", 0f, 0.25);
        _openTween.TweenProperty(_leftButton, "modulate:a", 0f, 0.1);
        _openTween.TweenProperty(_rightButton, "modulate:a", 0f, 0.1);
        _openTween.TweenProperty(_card, "modulate", StsColors.transparentWhite, 0.1);

        // TODO: Animate out the left/right arrows

        await ToSignal(_openTween, Tween.SignalName.Finished);
        Visible = false;
        NControllerManager.Instance!.RemoveScreen(this);
    }

    public void OnFocusScreen()
    {
        // Even though this screen doesn't have any nodes we want to focus right away
        // we want to make sure that we clear the controller focus from any previous screen.
        GetViewport().GuiReleaseFocus();
    }
    public void OnUnfocusScreen()
    {

    }
}
