using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;

public partial class NMapPointHistoryEntry : NClickableControl
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/climb_history_screen/map_point_history_entry");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    private Vector2 _baseScale = Vector2.One * 0.7f;
    private ClimbHistory _climbHistory = default!;
    private MapPointHistoryEntry _entry = default!;
    private int _floorNum;
    private ClimbHistoryPlayer? _player;
    private bool _hoverTipSet;
    private TextureRect _texture = default!;
    private TextureRect _outline = default!;

    private Tween? _animateInTween;
    private Tween? _hoverTween;
    private float _baseAngle;
    private bool _hurryUp;

    public override void _Ready()
    {
        _baseAngle = Rng.Chaotic.NextGaussianFloat(max: 5f);
        _texture = GetNode<TextureRect>("%Icon");
        _outline = GetNode<TextureRect>("%Outline");

        _texture.RotationDegrees = Rng.Chaotic.NextBool() ? _baseAngle : -_baseAngle;

        string? iconPath = ImageHelper.GetRoomIconPath(
            _entry.MapPointType,
            _entry.RoomTypes[0],
            _entry.MapPointType is MapPointType.Boss or MapPointType.Ancient ? _entry.ModelId : null
        );

        string? outlinePath = ImageHelper.GetRoomIconOutlinePath(
            _entry.MapPointType,
            _entry.RoomTypes[0],
            _entry.MapPointType is MapPointType.Boss or MapPointType.Ancient ? _entry.ModelId : null
        );

        if (iconPath != null)
        {
            _texture.Visible = true;
            _texture.Texture = PreloadManager.Cache.GetCompressedTexture2D(iconPath);
        }
        else
        {
            _texture.Visible = false;
        }

        if (outlinePath != null)
        {
            _outline.Visible = true;
            _outline.Texture = PreloadManager.Cache.GetCompressedTexture2D(outlinePath);
        }
        else
        {
            _outline.Visible = false;
        }

        Modulate = Modulate with { A = 1f };
        ConnectSignals();
    }

    public static NMapPointHistoryEntry Create(ClimbHistory history, MapPointHistoryEntry entry, int floorNum)
    {
        NMapPointHistoryEntry node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NMapPointHistoryEntry>();
        node._climbHistory = history;
        node._entry = entry;
        node._floorNum = floorNum;

        return node;
    }

    public void SetPlayer(ClimbHistoryPlayer player)
    {
        _player = player;
        _hoverTipSet = false;
    }

    protected override void OnFocus()
    {
        if (_player == null) throw new InvalidOperationException("Player has not been set!");

        Highlight();

        HoverTipAlignment alignment = HoverTip.GetHoverTipAlignment(this);
        NHoverTipSet tip = NHoverTipSet.CreateAndShowMapPointHistory(this, NMapPointHistoryHoverTip.Create(floorNum: _floorNum, playerId: _player.Id, historyEntry: _entry));
        // We call deferred here to make sure that the bounds are set correctly before we adjust the alignment
        Callable.From(() => tip.SetAlignment(this, alignment)).CallDeferred();
        tip.GlobalPosition += Vector2.Down * 96f;
    }

    protected override void OnUnfocus()
    {
        Unhighlight();
        NHoverTipSet.Remove(this);
    }

    public void Highlight()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenProperty(_texture, "scale", _baseScale * 1.5f, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_texture, "rotation_degrees", 0f, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_outline, "modulate", StsColors.halfTransparentWhite, 0.05)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    public void Unhighlight()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenProperty(_texture, "scale", _baseScale, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_texture, "rotation_degrees", _baseAngle, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_outline, "modulate", StsColors.quarterTransparentBlack, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    public async Task AnimateIn()
    {
        Scale = Vector2.One * 0.01f;
        _animateInTween = CreateTween().SetParallel();
        _animateInTween.TweenProperty(this, "scale", Vector2.One, _hurryUp ? 0.05 : 0.4)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Spring);
        _animateInTween.TweenProperty(this, "modulate", Colors.White, _hurryUp ? 0.05 : 0.1);
        _ = TaskHelper.RunSafely(DoAnimateInEffects());
        await ToSignal(_animateInTween, Tween.SignalName.Finished);
    }

    private async Task DoAnimateInEffects()
    {
        PlayerMapPointHistoryEntry playerEntry = _entry.GetEntry(_player!.Id);
        RoomType roomType = _entry.RoomTypes.Last();

        if (_climbHistory.MapPointHistory.Last().Last() == _entry && !_climbHistory.Win)
        {
            // Lost at this node
            NDebugAudioManager.Instance?.Play(TmpSfx.blockBroken, 1f, PitchVariance.Medium);
            NDebugAudioManager.Instance?.Play(TmpSfx.shortDeathStinger, 0.75f);
        }
        else if (roomType is RoomType.Monster or RoomType.Elite or RoomType.Boss)
        {
            await DoCombatAnimateInEffects(roomType);
        }
        else if (roomType == RoomType.Shop)
        {
            NDebugAudioManager.Instance?.Play(Rng.Chaotic.NextItem(TmpSfx.MerchantGreet)!, 1f, PitchVariance.Small);
        }
        else if (roomType == RoomType.Treasure)
        {
            SfxCmd.Play(PlayerCmd.goldMediumSfx);
        }
        else if (roomType == RoomType.RestSite)
        {
            if (playerEntry.RestSiteChoices.Contains("SMITH"))
            {
                NGame.Instance!.ScreenShakeTrauma(ShakeStrength.Medium);
                NDebugAudioManager.Instance?.Play(NCardSmithVfx.smithSfx, 1f, PitchVariance.Small);
            }
            else if (playerEntry.RestSiteChoices.Contains("HEAL"))
            {
                NDebugAudioManager.Instance?.Play(TmpSfx.restSiteHealBlanket, 1f, PitchVariance.Medium);
            }
            else if (playerEntry.RestSiteChoices.Contains("DIG"))
            {
                // TODO: Dig sfx
            }
            else if (playerEntry.RestSiteChoices.Contains("HATCH"))
            {
                SfxCmd.Play(ByrdSwoop.attackSfx);
            }
            else if (playerEntry.RestSiteChoices.Contains("LIFT"))
            {
                NGame.Instance?.ScreenShake(ShakeStrength.Strong, ShakeDuration.Short);
                // TODO: Lift sfx
            }
            else if (playerEntry.RestSiteChoices.Contains("MEND"))
            {
                // TODO: Mend sfx
            }
        }
        else if (roomType == RoomType.Event)
        {
            // This is probably not the right sound to use, but it's the one I associate the most with events
            NDebugAudioManager.Instance?.Play(TmpSfx.buttonHover, 0.75f, PitchVariance.Medium);
        }
    }

    private async Task DoCombatAnimateInEffects(RoomType roomType)
    {
        CharacterModel character = ModelDb.GetById<CharacterModel>(_player!.Character);

        if (roomType == RoomType.Monster)
        {
            NGame.Instance!.ScreenShakeTrauma(ShakeStrength.Weak);
            await PlaySfx(GetSmallHitSfx(character));
        }
        else if (roomType == RoomType.Elite)
        {
            NGame.Instance!.ScreenShakeTrauma(ShakeStrength.Medium);
            await PlaySfx(GetBigHitSfx(character));
        }
        else if (roomType == RoomType.Boss)
        {
            NGame.Instance!.ScreenShakeTrauma(ShakeStrength.Strong);
            await PlaySfx(GetBigHitSfx(character));
        }

        await Cmd.Wait(0.25f);

        foreach (ModelId monsterId in _entry.MonsterIds)
        {
            MonsterModel monster = ModelDb.GetById<MonsterModel>(monsterId);

            if (monster.HasDeathSfx)
            {
                SfxCmd.Play(monster.DeathSfx);
                await Cmd.Wait(0.25f);
            }
        }
    }

    private List<string> GetSmallHitSfx(CharacterModel character)
    {
        return character switch
        {
            Defect => [TmpSfx.slashAttack],
            Ironclad => [TmpSfx.bluntAttack],
            Necrobinder => [TmpSfx.slashAttack],
            Regent => [TmpSfx.slashAttack],
            Silent => [TmpSfx.slashAttack],
            _ => []
        };
    }

    private List<string> GetBigHitSfx(CharacterModel character)
    {
        return character switch
        {
            Defect => [ModelDb.Orb<LightningOrb>().LightningStrikeSfx],
            Ironclad => [TmpSfx.heavyAttack],
            Necrobinder => [TmpSfx.heavyAttack],
            Regent => [TmpSfx.heavyAttack],
            Silent => [TmpSfx.daggerThrow, TmpSfx.daggerThrow],
            _ => []
        };
    }

    private static async Task PlaySfx(List<string> sfxPaths)
    {
        for (int i = 0; i < sfxPaths.Count; i++)
        {
            string sfxPath = sfxPaths[i];

            if (sfxPath.StartsWith("event:"))
            {
                SfxCmd.Play(sfxPath);
            }
            else
            {
                NDebugAudioManager.Instance?.Play(sfxPath, 1f, PitchVariance.Medium);
            }

            if (i < sfxPaths.Count - 1)
            {
                await Cmd.Wait(0.1f);
            }
        }
    }

    public void HurryUp()
    {
        _hurryUp = true;
    }

    public void SetupForAnimation()
    {
        Modulate = StsColors.transparentBlack;
    }
}
