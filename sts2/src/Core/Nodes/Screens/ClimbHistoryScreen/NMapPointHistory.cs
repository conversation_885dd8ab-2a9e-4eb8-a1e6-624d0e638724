using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Relics;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;

public partial class NMapPointHistory : Control
{
    private Control _actContainer = default!;
    private List<NActHistoryEntry> ActHistories => _actContainer.GetChildren().OfType<NActHistoryEntry>().ToList();
    private List<NMapPointHistoryEntry> MapHistories => ActHistories.SelectMany(a => a.Entries).ToList();

    public override void _Ready()
    {
        _actContainer = GetNode<Control>("%Acts");
    }

    public void LoadHistory(ClimbHistory history)
    {
        foreach (Node child in _actContainer.GetChildren())
        {
            child.QueueFreeSafely();
        }

        int baseFloorCount = 0;

        for (int i = 0; i < history.MapPointHistory.Count; i++)
        {
            LocString act = ModelDb.GetById<ActModel>(history.Acts[i]).Title;
            _actContainer.AddChildSafely(NActHistoryEntry.Create(act, history, history.MapPointHistory[i], baseFloorCount));

            // Minus one to remove the map room from the count
            baseFloorCount += history.MapPointHistory[i].Count - 1;
        }

        // setting up navigation

        // default navigation seems to be working fine for the most part. just constrain the bounds so you can't
        // hover over anything else
        foreach (NActHistoryEntry actHistory in ActHistories)
        {
            actHistory.Entries.First().FocusNeighborLeft = actHistory.Entries.First().GetPath();
            actHistory.Entries.Last().FocusNeighborRight = actHistory.Entries.Last().GetPath();
        }
        ActHistories.FirstOrDefault()?.Entries.ForEach(e => e.FocusNeighborTop = e.GetPath());
        ActHistories.LastOrDefault()?.Entries.ForEach(e => e.FocusNeighborBottom = e.GetPath());

    }

    public void HurryUp()
    {
        foreach (NActHistoryEntry entry in _actContainer.GetChildren().OfType<NActHistoryEntry>().ToList())
        {
            entry.HurryUp();
        }
    }

    public void SetPlayer(ClimbHistoryPlayer player)
    {
        foreach (NActHistoryEntry entryNode in _actContainer.GetChildren().OfType<NActHistoryEntry>())
        {
            entryNode.SetPlayer(player);
        }
    }

    public void SetDeckHistory(NDeckHistory deckHistory)
    {
        deckHistory.Connect(NDeckHistory.SignalName.Hovered, Callable.From<NDeckHistoryEntry>(HighlightRelevantEntries));
        deckHistory.Connect(NDeckHistory.SignalName.Unhovered, Callable.From<NDeckHistoryEntry>(UnHighlightEntries));
    }

    public void SetRelicHistory(NRelicHistory relicHistory)
    {
        relicHistory.Connect(NRelicHistory.SignalName.Hovered, Callable.From<NRelicBasicHolder>(HighlightRelevantEntries));
        relicHistory.Connect(NRelicHistory.SignalName.Unhovered, Callable.From<NRelicBasicHolder>(UnHighlightEntries));
    }

    private void HighlightRelevantEntries(NDeckHistoryEntry historyEntry)
    {
        foreach (int i in historyEntry.FloorsAddedToDeck)
        {
            MapHistories[i - 1].Highlight();
        }
    }

    private void HighlightRelevantEntries(NRelicBasicHolder holder)
    {
        if (holder.Relic.Model.FloorAddedToDeck > 0)
        {
            MapHistories[holder.Relic.Model.FloorAddedToDeck - 1].Highlight();
        }
    }

    private void UnHighlightEntries(NRelicBasicHolder _)
    {
        UnHighlightEntries();
    }

    private void UnHighlightEntries(NDeckHistoryEntry _)
    {
        UnHighlightEntries();
    }

    private void UnHighlightEntries()
    {
        foreach (NMapPointHistoryEntry entry in MapHistories)
        {
            entry.Unhighlight();
        }
    }

    public async Task AnimateIn()
    {
        foreach (NActHistoryEntry entry in _actContainer.GetChildren().OfType<NActHistoryEntry>().ToList())
        {
            await entry.AnimateIn();
        }
    }

    public void InitForAnimation()
    {
        foreach (NActHistoryEntry entry in _actContainer.GetChildren().OfType<NActHistoryEntry>().ToList())
        {
            entry.SetupForAnimation();
        }
    }

    public void OnFocus()
    {
        MapHistories.FirstOrDefault()?.TryGrabFocus();
    }
}
