using Godot;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Modding;

namespace MegaCrit.Sts2.Core.Nodes.Debug;

public partial class NModdedWarning : Label
{
    [Export]
    public bool isShort;

    public override void _Ready()
    {
        Visible = ModManager.LoadedMods.Count > 0;

        if (isShort)
        {
            Text = $"MODDED ({ModManager.LoadedMods.Count})";
        }
        else
        {
            LocString loc = new("main_menu_ui", "MODDED_WARNING");
            loc.Add("count", ModManager.LoadedMods.Count);
            Text = loc.GetFormattedText();
        }
    }
}
