using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Relics;

/// <summary>
/// UI for holding relics. Is located under the top bar.
/// Positioning is handled by the HFlowContainer.
/// </summary>
public partial class NRelicInventory : FlowContainer
{
    [Signal]
    public delegate void RelicsChangedEventHandler();

    // Will be null until the GlobalUi has been initialized.
    private Player? _player;

    private readonly List<NRelicInventoryHolder> _relicNodes = [];
    public List<NRelicInventoryHolder> RelicNodes => _relicNodes;
    private Vector2 _originalPos;
    private Tween? _curTween;

    private Tween? _debugHideTween;
    private bool _isDebugHidden;

    public override void _Ready()
    {
        _originalPos = Position;

        Connect(Control.SignalName.FocusEntered, Callable.From(() => { _relicNodes[0].TryGrabFocus(); }));
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        ConnectPlayerEvents();
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        DisconnectPlayerEvents();
    }

    public void Initialize(ClimbState climbState)
    {
        DisconnectPlayerEvents();
        _player = LocalContext.GetMe(climbState)!;
        ConnectPlayerEvents();

        foreach (RelicModel relic in _player.Relics)
        {
            Add(relic, true);
        }
    }

    private void ConnectPlayerEvents()
    {
        if (_player == null) return;

        _player.RelicObtained += OnRelicObtained;
        _player.RelicRemoved += OnRelicRemoved;
    }

    private void DisconnectPlayerEvents()
    {
        if (_player == null) return;

        _player.RelicObtained -= OnRelicObtained;
        _player.RelicRemoved -= OnRelicRemoved;
    }

    private void Add(RelicModel relic, bool startsShown, int index = -1)
    {
        RelicFtueCheck(relic);
        NRelicInventoryHolder holder = NRelicInventoryHolder.Create(relic)!;
        holder.Inventory = this;

        if (index < 0)
        {
            _relicNodes.Add(holder);
        }
        else
        {
            _relicNodes.Insert(index, holder);
        }

        this.AddChildSafely(holder);
        MoveChild(holder, index);

        if (!startsShown)
        {
            // Since some relics have a phase of card picking or whatever else after they are obtained
            // and only animate in after that occurs, relics always start out alpha 0 and are only shown
            // after PlayNewlyAcquiredAnimation is called
            holder.Relic.Icon.Modulate = holder.Relic.Icon.Modulate with { A = 0 };
        }

        holder.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(_ => OnRelicClicked(relic)));
        holder.Connect(NClickableControl.SignalName.Focused, Callable.From<NClickableControl>(_ => OnRelicFocused(relic)));
        holder.Connect(NClickableControl.SignalName.Unfocused, Callable.From<NClickableControl>(_ => OnRelicUnfocused()));

        EmitSignal(SignalName.RelicsChanged);
    }

    /// <summary>
    /// Opens the Inspect Relic screen and allows the player to paginate through the rest of the top bar relics.
    /// </summary>
    private void OnRelicClicked(RelicModel model)
    {
        List<RelicModel> relics = [];

        foreach (NRelicInventoryHolder relic in _relicNodes)
        {
            relics.Add(relic.Relic.Model);
        }

        NGame.Instance!.InspectRelicScreen.Open(relics, model);
    }

    private void OnRelicFocused(RelicModel model)
    {
        ClimbManager.Instance.HoveredModelTracker.OnLocalRelicHovered(model);
    }

    private static void OnRelicUnfocused()
    {
        ClimbManager.Instance.HoveredModelTracker.OnLocalRelicUnhovered();
    }

    private void RelicFtueCheck(RelicModel relic)
    {
        if (relic.Rarity != RelicRarity.Starter && !SaveManager.Instance.SeenFtue(NObtainRelicFtue.id))
        {
            NModalContainer.Instance!.Add(NObtainRelicFtue.Create()!);
            SaveManager.Instance.MarkFtueAsComplete(NObtainRelicFtue.id);
        }
    }

    private void Remove(RelicModel relic)
    {
        if (!LocalContext.IsMine(relic)) return;

        NRelicInventoryHolder relicToRemove = _relicNodes.First(n => n.Relic.Model == relic);
        _relicNodes.Remove(relicToRemove);
        this.RemoveChildSafely(relicToRemove);

        EmitSignalRelicsChanged();
    }

    public void AnimateRelic(RelicModel relic, Vector2? startPosition = null, Vector2? startScale = null)
    {
        if (!LocalContext.IsMine(relic)) return;

        NRelicInventoryHolder relicNode = _relicNodes.First(n => n.Relic.Model == relic);
        TaskHelper.RunSafely(relicNode.PlayNewlyAcquiredAnimation(startPosition, startScale));
    }

    private void OnRelicObtained(RelicModel relic) => Add(relic, false, _player!.Relics.IndexOf(relic));

    private void OnRelicRemoved(RelicModel relic) => Remove(relic);

    public void AnimShow()
    {
        _curTween?.Kill();
        _curTween = CreateTween();
        _curTween.TweenProperty(this, "global_position:y", _originalPos.Y, 0.25)
            .SetTrans(Tween.TransitionType.Back)
            .SetEase(Tween.EaseType.Out);
    }

    public void AnimHide()
    {
        _curTween?.Kill();
        _curTween = CreateTween();
        _curTween.TweenProperty(this, "global_position:y", _originalPos.Y - 68f * GetLineCount() - 90f, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
    }

    public void ShowImmediately()
    {
        _curTween?.Kill();
        Position = Position with { Y = _originalPos.Y };
    }

    public void HideImmediately()
    {
        _curTween?.Kill();
        Position = Position with { Y = _originalPos.Y - 68f * GetLineCount() - 90f };
    }

    /// <summary>
    /// Used for trailers
    /// </summary>
    /// <param name="inputEvent"></param>
    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent.IsActionReleased(DebugHotkey.hideTopBar))
        {
            DebugHideTopBar();
        }
    }

    /// <summary>
    /// Used for trailers
    /// </summary>
    private void DebugHideTopBar()
    {
        if (_isDebugHidden)
        {
            AnimShow();
        }
        else
        {
            AnimHide();
        }

        _isDebugHidden = !_isDebugHidden;
    }
}
