using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Potions;

public partial class NPotionHolder : NClickableControl
{
    private static readonly HoverTip _emptyHoverTip = new(
        new LocString("static_hover_tips", "POTION_SLOT.title"),
        new LocString("static_hover_tips", "POTION_SLOT.description")
    );

    public NPotion? Potion { get; private set; }

    private TextureRect _emptyIcon = default!;

    public bool HasPotion => Potion != null;

    private NPotionPopup? _popup;
    private bool _potionTargeting;
    private bool _isUsable;
    private Tween? _emptyPotionTween;
    private Tween? _hoverTween;
    private bool _disabledUntilPotionRemoved;
    public bool IsPotionUsable => _popup!.IsUsable;
    private bool _isFocused;

    private CancellationTokenSource? _cancelGrayOutPotionSource;

    private static string ScenePath => SceneHelper.GetScenePath("/potions/potion_holder");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    public static NPotionHolder Create(bool isUsable)
    {
        NPotionHolder node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NPotionHolder>();
        node._isUsable = isUsable;
        return node;
    }

    public override void _Ready()
    {
        _emptyIcon = GetNode<TextureRect>("%EmptyIcon");
        ConnectSignals();
    }

    protected override void OnFocus()
    {
        if (_isFocused) return;

        _isFocused = true;

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();

        if (Potion != null)
        {
            Potion!.DoBounce();
            _hoverTween.TweenProperty(Potion, "scale", Vector2.One * 1.15f, 0.05);

            NDebugAudioManager.Instance?.Play(Rng.Chaotic.NextItem(TmpSfx.PotionSlosh)!, 0.5f, PitchVariance.Large);

            if (!IsInstanceValid(_popup))
            {
                NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, Potion.Model.HoverTips, HoverTipAlignment.Center);
                tip.GlobalPosition = GlobalPosition + Vector2.Down * Size.Y * 1.5f;
            }
        }
        else
        {
            _hoverTween.TweenProperty(_emptyIcon, "scale", Vector2.One * 1.15f, 0.05);

            NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _emptyHoverTip);
            tip.GlobalPosition = GlobalPosition + Vector2.Down * Size.Y * 1.5f;
            tip.SetAlignment(this, HoverTipAlignment.Center);
        }
    }

    protected override void OnUnfocus()
    {
        _isFocused = false;
        NHoverTipSet.Remove(this);

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();

        if (Potion != null)
        {
            if (!_disabledUntilPotionRemoved)
            {
                _hoverTween.TweenProperty(Potion, "scale", Vector2.One, 0.5)
                    .SetEase(Tween.EaseType.Out)
                    .SetTrans(Tween.TransitionType.Expo);
            }
        }
        else
        {
            _hoverTween.TweenProperty(_emptyIcon, "scale", Vector2.One, 0.5)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Expo);
        }
    }

    protected override void OnRelease()
    {
        if (_isUsable)
        {
            OpenPotionPopup();
        }
    }

    private void OpenPotionPopup()
    {
        if (HasPotion && !Potion!.Model.Owner.ClimbState.IsGameOver && !_disabledUntilPotionRemoved)
        {
            NHoverTipSet.Remove(this);
            _popup = NPotionPopup.Create(this);
            this.AddChildSafely(_popup);
        }
    }

    public void AddPotion(NPotion potion)
    {
        if (Potion != null)
        {
            throw new InvalidOperationException("Slot already contains a potion");
        }

        Potion = potion;
        _emptyPotionTween?.Kill();
        _emptyIcon.Modulate = Colors.Transparent;

        this.AddChildSafely(Potion);
        Potion.PivotOffset = Potion.Size * 0.5f;
    }

    public void DisableUntilPotionRemoved()
    {
        if (_popup != null && IsInstanceValid(_popup))
        {
            _popup.Remove();
        }

        _disabledUntilPotionRemoved = true;

        // If playing the potion takes a long time (e.g. because it's queued up behind some other actions), gray out
        // the potion holder until the potion is played. But delay it a little bit because, if the potion is immediately
        // used, then we don't want it to flicker gray and then back again.
        TaskHelper.RunSafely(GrayPotionHolderUntilPlayedAfterDelay());
    }

    private async Task GrayPotionHolderUntilPlayedAfterDelay()
    {
        _cancelGrayOutPotionSource = new CancellationTokenSource();
        await Task.Delay(100, _cancelGrayOutPotionSource.Token);
        if (!_cancelGrayOutPotionSource.IsCancellationRequested)
        {
            Modulate = StsColors.gray;
        }
    }

    public void CancelPotionUse()
    {
        _cancelGrayOutPotionSource?.Cancel();
        _disabledUntilPotionRemoved = false;
        Modulate = Colors.White;
    }

    public void RemoveUsedPotion()
    {
        if (Potion == null)
        {
            throw new InvalidOperationException("This slot doesn't contain a potion");
        }

        if (_popup != null && IsInstanceValid(_popup))
        {
            _popup.Remove();
        }

        _disabledUntilPotionRemoved = false;
        _cancelGrayOutPotionSource?.Cancel();
        Modulate = Colors.White;

        // do this just in case a new potion is added during the discard tween
        NPotion potionToRemove = Potion;
        Potion = null;

        Tween removeTween = CreateTween();
        removeTween.TweenProperty(potionToRemove, "scale", Vector2.Zero, 0.2f).SetEase(Tween.EaseType.In).SetTrans(Tween.TransitionType.Back).FromCurrent();
        removeTween.TweenCallback(Callable.From(() =>
        {
            this.RemoveChildSafely(potionToRemove);
            potionToRemove.QueueFreeSafely();
        }));

        _emptyPotionTween?.Kill();
        _emptyPotionTween = CreateTween();
        _emptyPotionTween.TweenProperty(_emptyIcon, "modulate", Colors.White, 0.2f)
            .SetDelay(0.2f);
    }

    public void DiscardPotion()
    {
        if (Potion == null)
        {
            throw new InvalidOperationException("This slot doesn't contain a potion");
        }

        if (_popup != null && IsInstanceValid(_popup))
        {
            _popup.Remove();
        }

        _disabledUntilPotionRemoved = false;
        _cancelGrayOutPotionSource?.Cancel();
        Modulate = Colors.White;

        // do this just in case a new potion is added during the discard tween
        NPotion potionToRemove = Potion;
        Potion = null;

        Tween discardTween = CreateTween();
        discardTween.TweenProperty(potionToRemove, "position:y", -100f, 0.4f)
            .SetEase(Tween.EaseType.In)
            .SetTrans(Tween.TransitionType.Back);

        discardTween.TweenCallback(Callable.From(() =>
        {
            this.RemoveChildSafely(potionToRemove);
            potionToRemove.QueueFreeSafely();
        }));

        _emptyPotionTween?.Kill();
        _emptyPotionTween = CreateTween();
        _emptyPotionTween.TweenProperty(_emptyIcon, "modulate", Colors.White, 0.2f)
            .FromCurrent()
            .SetDelay(0.2f);
    }

    /// <summary>
    /// Uses the potion.
    /// This may initiate targeting for single-targeted potions. If targeting is cancelled, the potion will not be used.
    /// </summary>
    public async Task UsePotion()
    {
        if (Potion == null)
        {
            Log.Warn("Tried to use potion in holder, but potion node is null!");
            return;
        }

        if (Potion.Model.Target is ActionTarget.AnyEnemy or ActionTarget.TargetedNoCreature ||
            (Potion.Model.Target == ActionTarget.AnyPlayer && Potion.Model.Owner.ClimbState.Players.Count > 1))
        {
            ClimbManager.Instance.HoveredModelTracker.OnLocalPotionSelected(Potion.Model);
            await TargetNode(Potion.Model.Target);
            ClimbManager.Instance.HoveredModelTracker.OnLocalPotionDeselected();
        }
        else
        {
            Potion.Model.EnqueueManualUse(Potion.Model.Owner.Creature);
            this.TryGrabFocus();
        }
    }

    private async Task TargetNode(ActionTarget actionTarget)
    {
        Vector2 startPos = GlobalPosition + Vector2.Right * Size.X * 0.5f + Vector2.Down * 50f;

        // Set up the targeting manager. This will control the drawing of the targeting arrow.
        NTargetManager targetManager = NTargetManager.Instance;

        bool usingController = NControllerManager.Instance!.IsUsingController;

        // TODO: make sure we cancel targeting if we switch controller types
        targetManager.StartTargeting(actionTarget, startPos, usingController ? TargetMode.Controller : TargetMode.ClickMouseToTarget, ShouldCancelTargeting, null);

        if (usingController && CombatManager.Instance.IsInProgress)
        {
            Creature owner = Potion!.Model.Owner.Creature;
            CombatState combatState = owner.CombatState!;

            IEnumerable<Creature> creatures = actionTarget switch
            {
                ActionTarget.AnyEnemy => combatState.GetOpponentsOf(owner),
                ActionTarget.AnyPlayer => combatState.GetTeammatesOf(owner),
                _ => throw new ArgumentOutOfRangeException(nameof(actionTarget), actionTarget, null)
            };

            List<Creature> validCreatures = creatures.Where(c => c.IsAlive).ToList();

            NCombatRoom.Instance!.RestrictControllerNavigation(validCreatures.Select(c => NCombatRoom.Instance.GetCreatureNode(c)!.Hitbox));
            NCombatRoom.Instance.GetCreatureNode(validCreatures.First())!.Hitbox.TryGrabFocus();
        }

        Node? targetNode = await targetManager.SelectionFinished();

        // Combat room may be null for special targeted potions that can be used outside of combat, like Foul Potion.
        NCombatRoom.Instance?.EnableControllerNavigation();

        if (targetNode != null)
        {
            Creature? target = targetNode switch
            {
                NCreature creatureNode => creatureNode.Entity,
                NMultiplayerPlayerState playerState => playerState.Player.Creature,
                NMerchantButton merchantButton => null,
                _ => throw new ArgumentOutOfRangeException(nameof(targetNode), targetNode, null),
            };

            Potion!.Model.EnqueueManualUse(target);
        }
        this.TryGrabFocus();
    }

    // In combat, we cancel targeting if an overlay shows up (e.g. Survivor was queued and interrupts targeting)
    // Out-of-combat, targeting can occur while an overlay is up (e.g. target another player with a Blood Potion while
    // looking at rewards)
    private bool ShouldCancelTargeting() => CombatManager.Instance.IsInProgress &&
        (NOverlayStack.Instance!.ScreenCount > 0 || NCapstoneContainer.Instance!.InUse);

    /// <summary>
    /// Makes all of your potions popup and make a slosh sound to remind players that they have potions.
    /// </summary>
    public async Task ShineOnStartOfCombat()
    {
        if (HasPotion)
        {
            Potion!.DoBounce();
            await Cmd.Wait(0.25f);
            NDebugAudioManager.Instance?.Play(Rng.Chaotic.NextItem(TmpSfx.PotionSlosh)!, 0.3f, PitchVariance.Large);
        }
    }
}
