using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Odds;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Climbs;

public class ClimbState : IClimbState, IPlayerCollection
{
    private readonly List<Player> _players = [];

    /// <summary>
    /// See <see cref="IClimbState.Players"/>
    /// </summary>
    public IReadOnlyList<Player> Players => _players;

    /// <summary>
    /// See <see cref="IClimbState.Acts"/>
    /// </summary>
    public IReadOnlyList<ActModel> Acts { get; }

    private int _currentActIndex;

    /// <summary>
    /// See <see cref="IClimbState.CurrentActIndex"/>
    /// </summary>
    public int CurrentActIndex
    {
        get => _currentActIndex;
        set
        {
            if (_currentActIndex == value) return;

            _visitedMapCoords.Clear();
            ActFloor = 0;
            _currentActIndex = value;
        }
    }

    /// <summary>
    /// See <see cref="IClimbState.Act"/>
    /// </summary>
    public ActModel Act => Acts[CurrentActIndex];

    /// <summary>
    /// See <see cref="IClimbState.Map"/>
    /// </summary>
    public ActMap Map { get; set; } = NullActMap.Instance;

    private readonly List<MapCoord> _visitedMapCoords = [];

    /// <summary>
    /// The MapCoords that have been visited within the current act.
    /// </summary>
    public IReadOnlyList<MapCoord> VisitedMapCoords => _visitedMapCoords;

    /// <summary>
    /// See <see cref="IClimbState.CurrentMapCoord"/>
    /// </summary>
    public MapCoord? CurrentMapCoord => _visitedMapCoords.Count == 0 ? null : _visitedMapCoords.Last();

    /// <summary>
    /// See <see cref="IClimbState.CurrentMapPoint"/>
    /// </summary>
    public MapPoint? CurrentMapPoint => CurrentMapCoord != null ? Map.GetPoint(CurrentMapCoord.Value) : null;

    /// <summary>
    /// See <see cref="IClimbState.CurrentLocation"/>
    /// </summary>
    public ClimbLocation CurrentLocation => new(CurrentMapCoord, CurrentActIndex);

    /// <summary>
    /// See <see cref="IClimbState.ActFloor"/>
    /// </summary>
    public int ActFloor { get; set; }

    /// <summary>
    /// See <see cref="IClimbState.TotalFloor"/>
    /// </summary>
    public int TotalFloor
    {
        get
        {
            int prevActFloors = 0;

            for (int i = 0; i < CurrentActIndex; i++)
            {
                prevActFloors += Acts[i].NumberOfFloors;
            }

            return prevActFloors + ActFloor;
        }
    }

    private readonly List<List<MapPointHistoryEntry>> _mapPointHistory = [];

    /// <summary>
    /// See <see cref="IClimbState.MapPointHistory"/>
    /// </summary>
    public IReadOnlyList<IReadOnlyList<MapPointHistoryEntry>> MapPointHistory => _mapPointHistory;

    /// <summary>
    /// See <see cref="IClimbState.CurrentMapPointHistoryEntry"/>
    /// </summary>
    public MapPointHistoryEntry? CurrentMapPointHistoryEntry => MapPointHistory.LastOrDefault()?.LastOrDefault();

    /// <summary>
    /// A stack of all the rooms the player is currently in.
    ///
    /// There will usually be exactly one room in here, but here are the reasons it may be a different number:
    /// * There may be 2+ rooms in here when a player makes a choice that spawns a new room without traveling to a new
    ///   map point. For example, in the Dense Vegetation event, when the player chooses the Nap option, a new
    ///   <see cref="CombatRoom"/> is pushed to this stack. Then, when that combat is over, we pop that room off the
    ///   stack, allowing us to return to the event.
    /// * There may be 0 rooms in here during brief windows, like in the middle of traveling to a new map point.
    ///
    /// Whenever we travel to a new map point, this stack is cleared and the first room for that map point is pushed
    /// onto it.
    /// </summary>
    private readonly List<AbstractRoom> _currentRooms = [];

    /// <summary>
    /// See <see cref="IClimbState.CurrentRoomCount"/>
    /// </summary>
    public int CurrentRoomCount => _currentRooms.Count;

    public AbstractRoom? CurrentRoom => _currentRooms.LastOrDefault();

    /// <summary>
    /// The room at the base of the rooms stack.
    /// Usually the same as CurrentRoom because there's usually only one room in the rooms stack. However, when there
    /// are multiple rooms in the stack (like at an event where one of the options starts a combat), this will be the
    /// first room that was entered in the current map point.
    /// Usually safe to treat as non-null, but may be null during brief windows, like while we're in the middle of
    /// traveling to a new map point.
    /// </summary>
    public AbstractRoom? BaseRoom => _currentRooms.FirstOrDefault();

    /// <summary>
    /// See <see cref="IClimbState.IsGameOver"/>
    /// </summary>
    public bool IsGameOver => Players.Count > 0 && Players.All(p => p.Creature.IsDead);

    /// <summary>
    /// See <see cref="IClimbState.AscensionLevel"/>
    /// </summary>
    public int AscensionLevel { get; }

    /// <summary>
    /// See <see cref="IClimbState.Rng"/>
    /// </summary>
    public ClimbRngSet Rng { get; }

    /// <summary>
    /// See <see cref="IClimbState.Odds"/>
    /// </summary>
    public ClimbOddsSet Odds { get; }

    /// <summary>
    /// See <see cref="IClimbState.SharedRelicGrabBag"/>
    /// </summary>
    public RelicGrabBag SharedRelicGrabBag { get; }

    private readonly HashSet<ModelId> _visitedEventIds = [];

    /// <summary>
    /// The IDs of all the events that have been visited during this climb.
    /// This allows us to avoid visiting the same shared event twice across multiple acts.
    /// </summary>
    public IReadOnlySet<ModelId> VisitedEventIds => _visitedEventIds;

    /// <summary>
    /// List of custom modifiers applied to this run, for daily or custom climbs.
    /// </summary>
    public IReadOnlyList<ModifierModel> Modifiers { get; }

    /// <summary>
    /// See <see cref="IClimbState.ExtraFields"/>.
    /// </summary>
    public ExtraClimbFields ExtraFields { get; private set; } = new();

    /// <summary>
    /// All cards that have been created within this state.
    /// This allows us to keep track of "floating" cards that have not been added to a deck (like card rewards or fake
    /// cards in upgrade previews).
    /// </summary>
    private readonly List<CardModel> _allCards = [];

    /// <summary>
    /// Create a ClimbState for a brand new climb.
    /// This will connect each player up with the ClimbState so their starting inventory works properly.
    /// </summary>
    /// <param name="players">The players that should be in the climb.</param>
    /// <param name="acts">The mutable acts that should be in the climb.</param>
    /// <param name="modifiers">The modifiers that are applied to the climb.</param>
    /// <param name="ascensionLevel">The ascension level that the climb should be played at.</param>
    /// <param name="seed">The seed that the climb's RNG should use.</param>
    public static ClimbState CreateForNewClimb(IReadOnlyList<Player> players, IReadOnlyList<ActModel> acts, IReadOnlyList<ModifierModel> modifiers, int ascensionLevel, string seed)
    {
        ClimbRngSet rngSet = new(seed);
        ClimbOddsSet odds = new(rngSet.UnknownMapPoint);
        ClimbState state = CreateShared(players, acts, modifiers, 0, rngSet, odds, new RelicGrabBag(true), ascensionLevel);

        foreach (Player player in players)
        {
            player.InitializeSeed(seed);

            foreach (CardModel card in player.Deck.Cards)
            {
                card.AfterCreated();
            }
        }

        return state;
    }

    /// <summary>
    /// Load a serialized ClimbState.
    /// </summary>
    public static ClimbState FromSerializable(SerializableClimb save)
    {
        List<SerializablePlayer> serializablePlayers = save.Players;
        List<Player> players = serializablePlayers.Select(Player.FromSerializable).ToList();
        ClimbRngSet rngSet = ClimbRngSet.FromSave(save.SerializableRng);

        ClimbState state = CreateShared(
            players,
            save.Acts.Select(ActModel.FromSave).ToList(),
            save.Modifiers.Select(ModifierModel.FromSerializable).ToList(),
            save.CurrentActIndex,
            rngSet,
            ClimbOddsSet.FromSerializable(save.SerializableOdds, rngSet.UnknownMapPoint),
            RelicGrabBag.FromSerializable(save.SerializableSharedRelicGrabBag),
            save.Ascension
        );

        state._visitedMapCoords.AddRange(save.VisitedMapCoords);
        state._visitedEventIds.UnionWith(save.EventsSeen);
        state._mapPointHistory.AddRange([..save.MapPointHistory]);
        state.ExtraFields = ExtraClimbFields.FromSerializable(save.ExtraFields);

        return state;
    }

    public static ClimbState CreateForTest(IReadOnlyList<Player>? players = null, IReadOnlyList<ActModel>? acts = null, IReadOnlyList<ModifierModel>? modifiers = null, int ascensionLevel = 0, string? seed = null)
    {
        seed ??= SeedHelper.GetRandomSeed();
        ClimbRngSet rngSet = new (seed);

        ClimbState state = CreateShared(
            players ?? [Player.CreateForNewClimb<Deprived>(NetSingleplayerGameService.defaultNetId)],
            (acts ?? ActModel.GetDefaultList()).Select(a => a.ToMutable()).ToList(),
            modifiers ?? [],
            0,
            rngSet,
            new ClimbOddsSet(rngSet.UnknownMapPoint),
            new RelicGrabBag(true),
            ascensionLevel
        );

        foreach (Player player in state.Players)
        {
            player.InitializeSeed(seed);
        }

        return state;
    }

    private static ClimbState CreateShared(
        IReadOnlyList<Player> players,
        IReadOnlyList<ActModel> acts,
        IReadOnlyList<ModifierModel> modifiers,
        int currentActIndex,
        ClimbRngSet rng,
        ClimbOddsSet odds,
        RelicGrabBag sharedRelicGrabBag,
        int ascensionLevel
    )
    {
        ClimbState state = new(players, acts, modifiers, currentActIndex, rng, odds, sharedRelicGrabBag, ascensionLevel);

        foreach (Player player in players)
        {
            player.ClimbState = state;

            // Cards are normally only added to a player's deck via IClimbState.CreateCard, but players may be created
            // with some initial cards in their deck, so we need to hook those initial cards up with this ClimbState.
            foreach (CardModel card in player.Deck.Cards)
            {
                state.AddCard(card, player);
            }
        }

        return state;
    }

    private ClimbState(
        IReadOnlyList<Player> players,
        IReadOnlyList<ActModel> acts,
        IReadOnlyList<ModifierModel> modifiers,
        int currentActIndex,
        ClimbRngSet rng,
        ClimbOddsSet odds,
        RelicGrabBag sharedRelicGrabBag,
        int ascensionLevel
    )
    {
        foreach (ActModel act in acts)
        {
            act.AssertMutable();
        }

        _players.AddRange(players);
        Acts = acts;
        Modifiers = modifiers;
        CurrentActIndex = currentActIndex;
        Rng = rng;
        Odds = odds;
        SharedRelicGrabBag = sharedRelicGrabBag;
        AscensionLevel = ascensionLevel;
    }

    #region Public player-related methods

    public int GetPlayerSlotIndex(Player player) => Players.IndexOf(player);
    public int GetPlayerSlotIndex(ulong netId) => Players.FirstIndex(p => p.NetId == netId);

    /// <summary>
    /// See <see cref="IClimbState.GetPlayer"/>.
    /// </summary>
    public Player? GetPlayer(ulong netId) => Players.FirstOrDefault(p => p.NetId == netId);

    #endregion

    #region Public card-related methods

    /// <summary>
    /// See <see cref="ICardScope.CreateCard{T}"/>.
    /// </summary>
    public T CreateCard<T>(Player owner) where T : CardModel => (T)CreateCard(ModelDb.Card<T>(), owner);

    /// <summary>
    /// See <see cref="ICardScope.CreateCard"/>.
    /// </summary>
    public CardModel CreateCard(CardModel canonicalCard, Player owner)
    {
        CardModel card = canonicalCard.ToMutable();
        AddCard(card, owner);
        card.AfterCreated();

        return card;
    }

    /// <summary>
    /// See <see cref="ICardScope.CloneCard"/>.
    /// </summary>
    public CardModel CloneCard(CardModel mutableCard)
    {
        CardModel card = (CardModel)mutableCard.ClonePreservingMutability();
        AddCard(card);

        return card;
    }

    /// <summary>
    /// See <see cref="ICardScope.AddCard"/>.
    /// </summary>
    public void AddCard(CardModel card, Player owner)
    {
        card.Owner = owner;
        AddCard(card);
    }

    /// <summary>
    /// See <see cref="ICardScope.RemoveCard"/>.
    /// </summary>
    public void RemoveCard(CardModel card)
    {
        _allCards.Remove(card);
        card.Owner = null!;
    }

    /// <summary>
    /// See <see cref="IClimbState.ContainsCard"/>.
    /// </summary>
    public bool ContainsCard(CardModel card) => _allCards.Contains(card);

    /// <summary>
    /// See <see cref="IClimbState.LoadCard"/>
    /// </summary>
    public CardModel LoadCard(SerializableCard serializableCard, Player owner)
    {
        CardModel card = CardModel.FromSerializable(serializableCard);
        AddCard(card, owner);

        return card;
    }

    private void AddCard(CardModel card)
    {
        card.AssertMutable();
        _allCards.Add(card);
    }

    #endregion

    /// <summary>
    /// Add the specified coord to the list of visited map coords.
    /// </summary>
    public void AddVisitedMapCoord(MapCoord coord)
    {
        if (_visitedMapCoords.Contains(coord))
        {
            throw new InvalidOperationException($"Already visited map coord {coord}.");
        }

        _visitedMapCoords.Add(coord);
    }

    /// <summary>
    /// Pop the current room off the stack of rooms that we're in.
    /// </summary>
    /// <returns>The removed room.</returns>
    /// <exception cref="InvalidOperationException">If we aren't in any rooms.</exception>
    public AbstractRoom PopCurrentRoom()
    {
        if (_currentRooms.Count == 0) throw new InvalidOperationException("Not in any rooms.");

        AbstractRoom room = _currentRooms.Last();
        _currentRooms.RemoveAt(_currentRooms.Count - 1);

        return room;
    }

    /// <summary>
    /// Push the specified room onto the stack of rooms that we're in.
    /// </summary>
    /// <exception cref="InvalidOperationException">If we're already in the specified room.</exception>
    public void PushRoom(AbstractRoom room)
    {
        if (_currentRooms.Contains(room)) throw new InvalidOperationException("Already in this room.");
        _currentRooms.Add(room);
    }

    /// <summary>
    /// Add the specified event to the list of visited events.
    /// </summary>
    public void AddVisitedEvent(EventModel eventModel)
    {
        _visitedEventIds.Add(eventModel.Id);
    }

    /// <summary>
    /// See <see cref="IClimbState.AppendToMapPointHistory"/>.
    /// </summary>
    public void AppendToMapPointHistory(MapPointType mapPointType, RoomType initialRoomType)
    {
        // If we are in a new act, add a new list for the new act.
        if (_mapPointHistory.Count <= CurrentActIndex)
        {
            // Add empty lists for the missing acts (if we are jumping to a later act via the console command)
            int missingEntryCount = CurrentActIndex + 1 - _mapPointHistory.Count;

            for (int i = 0; i < missingEntryCount; i++)
            {
                _mapPointHistory.Add([]);
            }
        }

        MapPointHistoryEntry entry = new(mapPointType, this);
        entry.RoomTypes.Add(initialRoomType);
        _mapPointHistory[CurrentActIndex].Add(entry);
    }

    /// <summary>
    /// See <see cref="IClimbState.GetHistoryEntryFor"/>.
    /// </summary>
    public MapPointHistoryEntry? GetHistoryEntryFor(ClimbLocation location)
    {
        // Normally the player always ascends one act and row at a time, but we should account for players debug-traveling to rooms
        if (location.actIndex >= _mapPointHistory.Count ||
            location.coord == null ||
            location.coord?.row >= _mapPointHistory[location.actIndex].Count)
        {
            return null;
        }

        return _mapPointHistory[location.actIndex][location.coord!.Value.row];
    }
}
